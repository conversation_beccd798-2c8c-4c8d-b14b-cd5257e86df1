import prisma from '../../config/prismaClient';
import { FoodItem } from './food.types';
import { minioStorageClient, BUCKETS, StorageFile } from '../../services/minio-storage.service';
import { logger } from '../../utils/logger';

export class FoodService {
  private validateImageFile(file: { buffer: Buffer; mimetype: string }): void {
    const bucketConfig = minioStorageClient.getBucketConfig(BUCKETS.FOOD_IMAGES);
    
    if (!bucketConfig.allowedMimeTypes?.includes(file.mimetype)) {
      throw new Error(`Invalid image type. Allowed types: ${bucketConfig.allowedMimeTypes?.join(', ')}`);
    }
    if (bucketConfig.fileSizeLimit && file.buffer.length > bucketConfig.fileSizeLimit) {
      throw new Error(`Image size exceeds maximum limit of ${bucketConfig.fileSizeLimit / (1024 * 1024)}MB`);
    }
  }

  private async uploadImage(file: Express.Multer.File | { buffer: Buffer; filename: string; mimetype: string }): Promise<string> {
    try {
      logger.info('[FOOD] Uploading image before creating food item');
      // Convert the file object to match StorageFile if needed
      const storageFile: StorageFile = 'fieldname' in file ? file : {
        fieldname: 'image',
        originalname: file.filename,
        encoding: '7bit',
        mimetype: file.mimetype,
        buffer: file.buffer,
        size: file.buffer.length,
        filename: file.filename
      };
      
      // Use the BUCKETS constant for consistency
      const bucketName = BUCKETS.FOOD_IMAGES;
      logger.info(`[FOOD] Using bucket: ${bucketName}`);
      
      return await minioStorageClient.uploadImage(storageFile, bucketName);
    } catch (error) {
      logger.error('[FOOD] Error uploading image:', error);
      throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async deleteImage(imageUrl: string): Promise<void> {
    try {
      if (imageUrl) {
        await minioStorageClient.deleteImage(imageUrl, BUCKETS.FOOD_IMAGES);
      }
    } catch (error) {
      logger.error('[FOOD] Error deleting image:', error);
      throw new Error('Failed to delete image');
    }
  }

  async createFoodItem(foodItem: Omit<FoodItem, 'id'>, imageFile?: { buffer: Buffer; filename: string; mimetype: string }): Promise<FoodItem> {
    let imageUrl: string | null = null;

    logger.info('[FOOD] Starting food item creation', {
      name: foodItem.name,
      price: foodItem.price,
      seller_name: foodItem.seller_name,
      community_id: foodItem.community_id,
      has_image: !!imageFile
    });

    // Validate image if provided
    if (imageFile) {
      logger.info('[FOOD] Validating image file', {
        filename: imageFile.filename,
        mimetype: imageFile.mimetype,
        size: imageFile.buffer.length
      });
      this.validateImageFile(imageFile);
    }

    try {
      // If image is provided, upload it first
      if (imageFile) {
        logger.info('[FOOD] Uploading image before creating food item');
        imageUrl = await this.uploadImage(imageFile);
        logger.info('[FOOD] Image uploaded successfully', {
          image_url: imageUrl
        });
      }

      // Create the food item with the image URL
      const createdItem = await prisma.foodItem.create({
        data: {
          name: foodItem.name,
          description: foodItem.description,
          price: foodItem.price,
          community_id: foodItem.community_id,
          seller_name: foodItem.seller_name,
          seller_id: foodItem.seller_id,
          contact_info: foodItem.contact_info,
          pickup_details: foodItem.pickup_details,
          inline_photo_id: foodItem.inline_photo_id,
          data_ai_hint: foodItem.data_ai_hint,
          created_at: new Date(),
          icon: foodItem.icon,
          icon_set: foodItem.icon_set,
          image_url: imageUrl,
        }
      });

      logger.info('[FOOD] Food item created successfully', {
        id: createdItem.id,
        name: createdItem.name,
        price: createdItem.price,
        seller_name: createdItem.seller_name,
        has_image: !!createdItem.image_url,
        image_url: createdItem.image_url
      });

      return createdItem;
    } catch (error) {
      logger.error('[FOOD] Error creating food item', {
        error: error instanceof Error ? error.message : 'Unknown error',
        food_item: {
          name: foodItem.name,
          price: foodItem.price,
          seller_name: foodItem.seller_name
        }
      });
      // If anything fails, clean up the uploaded image
      if (imageUrl) {
        try {
          await this.deleteImage(imageUrl);
          logger.info('[FOOD] Cleaned up image after error', { image_url: imageUrl });
        } catch (deleteError) {
          logger.error('[FOOD] Failed to clean up image after error:', deleteError);
        }
      }
      throw error;
    }
  }

  async getFoodItems(): Promise<FoodItem[]> {
    return prisma.foodItem.findMany({
      orderBy: { created_at: 'desc' },
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        community_id: true,
        seller_name: true,
        seller_id: true,
        contact_info: true,
        pickup_details: true,
        inline_photo_id: true,
        data_ai_hint: true,
        created_at: true,
        icon: true,
        icon_set: true,
        image_url: true,
      }
    });
  }

  async getFoodItem(id: string): Promise<FoodItem | null> {
    const foodItem = await prisma.foodItem.findUnique({
      where: { id }
    });

    if (!foodItem) {
      return null;
    }

    // If there's an image URL, verify the image exists
    if (foodItem.image_url) {
      const imagePath = foodItem.image_url.split('/').pop();
      if (imagePath) {
        const imageExists = await minioStorageClient.getImage(foodItem.image_url, BUCKETS.FOOD_IMAGES);
        if (!imageExists) {
          // If image doesn't exist, update the food item to remove the URL
          return prisma.foodItem.update({
            where: { id },
            data: { image_url: null }
          });
        }
      }
    }

    return foodItem;
  }

  async getFoodItemByImageId(imageId: string): Promise<FoodItem | null> {
    // Find food item where the image_url contains the imageId
    const foodItem = await prisma.foodItem.findFirst({
      where: {
        image_url: {
          contains: imageId
        }
      }
    });

    return foodItem;
  }

  async updateFoodItem(id: string, data: Partial<FoodItem>, imageFile?: { buffer: Buffer; filename: string; mimetype: string }): Promise<FoodItem | null> {
    const existingItem = await prisma.foodItem.findUnique({
      where: { id }
    });

    if (!existingItem) {
      return null;
    }

    let imageUrl = data.image_url;

    if (imageFile) {
      this.validateImageFile(imageFile);
      try {
        // Delete old image if it exists
        if (existingItem.image_url) {
          await this.deleteImage(existingItem.image_url);
        }

        // Upload new image
        imageUrl = await this.uploadImage(imageFile);
      } catch (error) {
        logger.error('[FOOD] Failed to update food image:', error);
        throw new Error('Failed to update food image');
      }
    }

    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.community_id !== undefined) updateData.community_id = data.community_id;
    if (data.seller_name !== undefined) updateData.seller_name = data.seller_name;
    if (data.seller_id !== undefined) updateData.seller_id = data.seller_id;
    if (data.contact_info !== undefined) updateData.contact_info = data.contact_info;
    if (data.pickup_details !== undefined) updateData.pickup_details = data.pickup_details;
    if (data.inline_photo_id !== undefined) updateData.inline_photo_id = data.inline_photo_id;
    if (data.data_ai_hint !== undefined) updateData.data_ai_hint = data.data_ai_hint;
    if (data.icon !== undefined) updateData.icon = data.icon;
    if (data.icon_set !== undefined) updateData.icon_set = data.icon_set;
    if (imageUrl !== undefined) updateData.image_url = imageUrl;

    return prisma.foodItem.update({
      where: { id },
      data: updateData
    });
  }

  async deleteFoodItem(id: string): Promise<boolean> {
    const foodItem = await prisma.foodItem.findUnique({
      where: { id }
    });

    if (!foodItem) {
      return false;
    }

    // Delete image if it exists
    if (foodItem.image_url) {
      try {
        await this.deleteImage(foodItem.image_url);
      } catch (error) {
        logger.error('[FOOD] Error deleting food image:', error);
        // Continue with deletion even if image deletion fails
      }
    }

    await prisma.foodItem.delete({
      where: { id }
    });

    return true;
  }
} 