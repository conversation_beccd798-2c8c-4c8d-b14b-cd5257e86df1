import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import type { Event as EventType } from '../../types';
import { eventsService } from '@services/events';
import type { EventsStackParamList } from '@navigation/types';
import type { Event as ServiceEvent } from '@services/events';
import { getTextAlign } from '@utils/rtl';
import Header from '@components/Header';
import { SearchInput } from '../../components/SearchInput';
import { getEventColor, getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';

type EventsListScreenNavigationProp = StackNavigationProp<EventsStackParamList, 'EventsList'>;

export default function EventsListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<EventsListScreenNavigationProp>();
  const [events, setEvents] = useState<EventType[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadEvents();
  }, []);

  // Refresh events when screen comes into focus (e.g., after creating a new event)
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔄 EventsList screen focused - refreshing events...');
      loadEvents();
    }, [])
  );

  const loadEvents = async () => {
    try {
      console.log('🔄 Loading events...');
      const fetchedEvents = await eventsService.getEventsFromBackend();
      // Transform the events to match the EventType interface
      const transformedEvents = fetchedEvents.map((event: ServiceEvent) => ({
        ...event,
        rsvp: (event as any).rsvp || [],
        comments: (event as any).comments || [],
        reactions: (event as any).reactions || []
      }));
      setEvents(transformedEvents);
      console.log('✅ Events loaded successfully:', transformedEvents.length);
    } catch (error) {
      console.error('❌ Error loading events:', error);
      // Fallback to mock data if both backend and Supabase fail
      const mockEvents: EventType[] = [
        {
          id: '1',
          title: 'יריד קיץ קהילתי',
          description: 'יריד קהילתי שנתי עם משחקים, אוכל ומוכרים מקומיים. מושלם למשפחות!',
          type: 'community',
          createdBy: 'admin',
          date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          location: 'הפארק המרכזי',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
        {
          id: '2',
          title: 'סדנת גינון קהילתי',
          description: 'למדו טכניקות גינון בר-קיימא עם המומחים המקומיים שלנו.',
          type: 'community',
          createdBy: 'user123',
          date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
          location: 'מרכז הגינון',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
        {
          id: '3',
          title: 'טורניר כדורגל נוער',
          description: 'טורניר כדורגל לילדים מקומיים. בואו לעודד את הספורטאים הצעירים שלנו!',
          type: 'community',
          createdBy: 'admin',
          date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          location: 'מגרש הספורט',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
        {
          id: '4',
          title: 'לילת קולנוע תחת הכוכבים',
          description: 'הקרנת סרט חיצונית ידידותית למשפחה. הביאו שמיכות וחטיפים.',
          type: 'community',
          createdBy: 'user456',
          date: new Date(Date.now() + 17 * 24 * 60 * 60 * 1000),
          location: 'כיכר העיר',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
      ];
      setEvents(mockEvents);
      console.log('⚠️ Using mock data due to API failure');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadEvents();
  };

  const handleAddEvent = () => {
    navigation.navigate('CreateEvent');
  };

  const handleEventPress = (eventId: string) => {
    navigation.navigate('EventDetails', { eventId });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatDateBox = (date: Date) => {
    const day = date.getDate().toString();
    const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
    return { day, month };
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderEventItem = ({ item, index }: { item: EventType; index: number }) => {
    const dateBox = formatDateBox(item.date);
    const time = formatTime(item.date);
    const dateBoxColor = getEventColor(index);
    const attendeeCount = (item as any).attendees || item.rsvp?.filter(r => r.status === 'attending').length || 0;

    return (
      <TouchableOpacity
        style={styles.eventCard}
        onPress={() => handleEventPress(item.id)}
      >
        <View style={[styles.eventDateBox, { backgroundColor: dateBoxColor }]}>
          <Text style={styles.eventDay}>{dateBox.day}</Text>
          <Text style={styles.eventMonth}>{dateBox.month}</Text>
        </View>

        <View style={styles.cardContent}>
          <Text style={styles.eventTitle}>{item.title}</Text>
          <Text style={styles.eventDescription}>{item.description}</Text>

          <View style={styles.eventMeta}>
            <Text style={styles.metaText}>🕐 {time}</Text>
            <Text style={styles.metaText}>📍 {item.location}</Text>
            <Text style={styles.metaText}>👥 {attendeeCount} going</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען אירועים...</Text>
      </View>
    );
  }

  const addButton = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleAddEvent}
    >
      <Text style={styles.addButtonText}>+ הוסף</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Header
        title="📅 אירועי קהילה"
        showBackButton={true}
        rightComponent={addButton}
      />

      {/* Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <SearchInput
          placeholder="🔍 חפש אירועים..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        {/* Events List */}
        <FlatList
          data={filteredEvents}
          renderItem={({ item, index }) => renderEventItem({ item, index })}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* FAB */}
      <TouchableOpacity style={styles.fab} onPress={handleAddEvent}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('sm'),
    borderRadius: getBorderRadius('full'),
  },
  addButtonText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  content: {
    flex: 1,
    padding: getSpacing('lg'),
  },
  listContainer: {
    paddingBottom: 100,
  },
  eventCard: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    flexDirection: 'row',
    alignItems: 'center',
    ...getShadow('sm'),
  },
  eventDateBox: {
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('sm'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: getSpacing('lg'),
    minWidth: 50,
    minHeight: 50,
  },
  eventDay: {
    ...getTypography('lg', 'bold'),
    color: getColor('white'),
    lineHeight: 20,
  },
  eventMonth: {
    ...getTypography('xs', 'bold'),
    color: getColor('white'),
    textTransform: 'uppercase',
  },
  cardContent: {
    flex: 1,
  },
  eventTitle: {
    ...getTypography('base', 'bold'),
    color: getColor('neutral', 800),
    marginBottom: getSpacing('xs'),
    textAlign: 'right',
  },
  eventDescription: {
    ...getTypography('xs'),
    color: getColor('neutral', 500),
    lineHeight: 16,
    marginBottom: getSpacing('sm'),
    textAlign: 'right',
  },
  eventMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getSpacing('lg'),
    flexWrap: 'wrap',
  },
  metaText: {
    ...getTypography('xs'),
    color: getColor('neutral', 400),
  },
  fab: {
    position: 'absolute',
    bottom: getSpacing('xl'),
    right: getSpacing('xl'),
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: getColor('primary'),
    justifyContent: 'center',
    alignItems: 'center',
    ...getShadow('lg'),
  },
  fabText: {
    ...getTypography('2xl', 'bold'),
    color: getColor('white'),
  },
});
