import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../contexts/AuthContext';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';
import { Input } from '../../components/paper/Input';

export default function RegisterScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { register } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    dateOfBirth: new Date(),
    userType: 'ADULT' as 'ADULT' | 'YOUTH' | 'CHILD' | 'EXTERNAL',
  });
  const [loading, setLoading] = useState(false);

  const handleRegister = async () => {
    console.log('🚀 Registration button clicked');

    if (!formData.email || !formData.password || !formData.firstName || !formData.lastName) {
      console.log('❌ Validation failed: Missing required fields');
      Alert.alert(t('common.error'), t('auth.fillAllFields'));
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      console.log('❌ Validation failed: Password mismatch');
      Alert.alert(t('common.error'), t('auth.passwordMismatch'));
      return;
    }

    console.log('✅ Validation passed, starting registration...');
    setLoading(true);

    try {
      console.log('📝 Registering user with data:', {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        userType: formData.userType,
        phone: formData.phone ? 'provided' : 'not provided'
      });

      await register({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        dateOfBirth: formData.dateOfBirth,
        userType: formData.userType,
      });

      console.log('🎉 Registration successful!');
      // Navigation will be handled automatically by AuthContext auth state change
    } catch (error: any) {
      console.error('💥 Registration failed:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      // Provide more specific error messages
      let errorMessage = error.message || t('auth.registerError');

      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'כתובת האימייל כבר בשימוש';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'הסיסמה חלשה מדי - נדרשים לפחות 6 תווים';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'כתובת אימייל לא תקינה';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'בעיית רשת - בדוק את החיבור לאינטרנט';
      }

      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title={t('auth.register')}
        showBackButton={true}
      />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <WebCompatibleScrollView
          contentContainerStyle={styles.scrollContent}
          headerHeight={60}
        >
          <View style={styles.content}>
          <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
            צור חשבון חדש
          </Text>
          <Text style={[styles.infoText, { textAlign: getTextAlign() }]}>
            💡 המשתמש הראשון שנרשם יהפוך למנהל העל של המערכת
          </Text>

          <View style={styles.form}>
            <Input
              label={t('auth.email')}
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholder={t('auth.email')}
            />

            <Input
              label="שם פרטי *"
              value={formData.firstName}
              onChangeText={(text) => setFormData({ ...formData, firstName: text })}
              placeholder="שם פרטי *"
            />

            <Input
              label="שם משפחה *"
              value={formData.lastName}
              onChangeText={(text) => setFormData({ ...formData, lastName: text })}
              placeholder="שם משפחה *"
            />

            <Input
              label="טלפון (אופציונלי)"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              keyboardType="phone-pad"
              placeholder="טלפון (אופציונלי)"
            />

            <Input
              label={t('auth.password')}
              value={formData.password}
              onChangeText={(text) => setFormData({ ...formData, password: text })}
              secureTextEntry
              placeholder={t('auth.password')}
            />

            <Input
              label={t('auth.confirmPassword')}
              value={formData.confirmPassword}
              onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
              secureTextEntry
              placeholder={t('auth.confirmPassword')}
            />

            <View style={styles.roleContainer}>
              <Text style={[styles.roleLabel, { textAlign: getTextAlign() }]}>
                סוג המשתמש:
              </Text>
              <View style={styles.roleButtons}>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'ADULT' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'ADULT' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'ADULT' && styles.roleButtonTextActive
                  ]}>מבוגר</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'YOUTH' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'YOUTH' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'YOUTH' && styles.roleButtonTextActive
                  ]}>נוער</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'CHILD' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'CHILD' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'CHILD' && styles.roleButtonTextActive
                  ]}>ילד</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'EXTERNAL' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'EXTERNAL' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'EXTERNAL' && styles.roleButtonTextActive
                  ]}>חיצוני</Text>
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.button, loading && styles.buttonDisabled]}
              onPress={handleRegister}
              disabled={loading}
            >
              <Text style={styles.buttonText}>
                {loading ? 'נרשם...' : t('auth.registerButton')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.linkButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Text style={styles.linkText}>יש לך כבר חשבון? התחבר כאן</Text>
            </TouchableOpacity>
          </View>
          </View>
        </WebCompatibleScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...createWebCompatibleContainerStyle(0),
    backgroundColor: '#f5f5f5',
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    ...createWebCompatibleScrollContentStyle(20),
    flexGrow: 1,
  },
  content: {
    padding: 20,
  },
  subtitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 30,
  },
  form: {
    width: '100%',
  },
  roleContainer: {
    marginBottom: 20,
  },
  roleLabel: {
    fontSize: 16,
    marginBottom: 10,
    color: '#333',
  },
  roleButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  roleButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  roleButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  roleButtonText: {
    color: '#333',
    fontSize: 14,
  },
  roleButtonTextActive: {
    color: 'white',
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 15,
  },
  linkText: {
    color: '#007AFF',
    fontSize: 14,
  },
});
