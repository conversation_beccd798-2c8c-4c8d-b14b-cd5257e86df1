import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon, IconPicker } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { FoodStackParamList } from '@navigation/types';
import { StackNavigationProp } from '@react-navigation/stack';
import { IconSet } from '@components';
import { CreateFoodItemData } from '../../types/food';
import { apiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import * as ImagePicker from 'expo-image-picker';
import { Input } from '../../components/paper/Input';

type CreateFoodScreenNavigationProp = StackNavigationProp<FoodStackParamList, 'CreateFood'>;

export default function CreateFoodScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateFoodScreenNavigationProp>();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [pickupDetails, setPickupDetails] = useState('');
  const [contactInfo, setContactInfo] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('🍲');
  const [selectedIconSet, setSelectedIconSet] = useState<IconSet>('MaterialCommunityIcons');
  const [loading, setLoading] = useState(false);
  const [image, setImage] = useState<string | null>(null);
  const { user } = useAuth();

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      console.log('❌ Image picker permission denied');
      Alert.alert('Permission needed', 'Please grant permission to access your photos');
      return;
    }

    console.log('🔄 Opening image picker');
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      console.log('✅ Image selected:', {
        uri: result.assets[0].uri,
        width: result.assets[0].width,
        height: result.assets[0].height,
        type: result.assets[0].type
      });
      setImage(result.assets[0].uri);
    } else {
      console.log('ℹ️ Image selection canceled');
    }
  };

  const handleSubmit = async () => {
    if (!name || !description || !price || !contactInfo) {
      console.log('❌ Form validation failed:', {
        hasName: !!name,
        hasDescription: !!description,
        hasPrice: !!price,
        hasContactInfo: !!contactInfo
      });
      Alert.alert('שגיאה', 'אנא מלא את כל השדות החובה');
      return;
    }

    setLoading(true);
    try {
      const sellerName = user?.first_name && user?.last_name
        ? `${user.first_name} ${user.last_name}`
        : user?.first_name || user?.email || 'אנונימי';

      console.log('🔄 Starting food item creation:', {
        name,
        price,
        sellerName,
        hasImage: !!image
      });

      const foodData: CreateFoodItemData = {
        name,
        description,
        price,
        pickupDetails,
        contactInfo,
        icon: selectedIcon,
        iconSet: selectedIconSet,
        sellerName,
      };

      // Create FormData to handle image upload
      const formData = new FormData();
      formData.append('name', foodData.name);
      formData.append('description', foodData.description);
      formData.append('price', foodData.price);
      formData.append('pickupDetails', foodData.pickupDetails ?? '');
      formData.append('contactInfo', foodData.contactInfo);
      formData.append('icon', foodData.icon ?? '');
      formData.append('iconSet', foodData.iconSet ?? '');
      formData.append('sellerName', foodData.sellerName ?? '');

      if (image) {
        const imageUri = image;
        const filename = imageUri.split('/').pop() ?? 'image.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';
        
        console.log('📸 Preparing image for upload:', {
          filename,
          type,
          uri: imageUri
        });

        try {
          // Convert base64 to blob
          const response = await fetch(imageUri);
          const blob = await response.blob();
          
          // Create a file object that matches what the server expects
          const imageFile = new File([blob], filename, { type });
          
          // Append the image file to formData
          formData.append('image', imageFile);
          console.log('✅ Image prepared and added to form data');
        } catch (error) {
          console.error('❌ Error preparing image:', error);
        }
      }

      console.log('📦 Submitting food item data');
      const response = await apiService.createFoodItem(formData);
      
      if (response.data) {
        console.log('✅ Food item created successfully:', {
          id: response.data.id,
          name: response.data.name,
          hasImage: !!response.data.image
        });
        navigation.goBack();
      } else {
        console.error('❌ Failed to create food item:', response.error);
        Alert.alert('שגיאה', 'אירעה שגיאה ביצירת המוצר');
      }
    } catch (error) {
      console.error('❌ Error creating food item:', error);
      Alert.alert('שגיאה', 'אירעה שגיאה ביצירת המוצר');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="🍲 פרסם מוצר חדש"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.form}>
          <IconPicker
            selectedIcon={selectedIcon}
            onIconSelect={setSelectedIcon}
            label="בחר אייקון למוצר"
            defaultTab="food"
          />

          <View style={styles.imageUploadContainer}>
            <TouchableOpacity style={styles.imageUploadButton} onPress={pickImage}>
              {image ? (
                <Image source={{ uri: image }} style={styles.uploadedImage} />
              ) : (
                <View style={styles.uploadPlaceholder}>
                  <Icon
                    name="camera"
                    iconSet="MaterialCommunityIcons"
                    size={32}
                    color={getColor('neutral', 400)}
                  />
                  <Text style={styles.uploadText}>הוסף תמונה</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Input
              label="שם המוצר"
              value={name}
              onChangeText={setName}
              placeholder="לדוגמה: עוגת שוקולד"
              error={!name ? 'שדה חובה' : undefined}
            />
          </View>

          <View style={styles.inputGroup}>
            <Input
              label="תיאור"
              value={description}
              onChangeText={setDescription}
              placeholder="תאר את המוצר שלך..."
              multiline
              numberOfLines={4}
              error={!description ? 'שדה חובה' : undefined}
            />
          </View>

          <View style={styles.inputGroup}>
            <Input
              label="מחיר"
              value={price}
              onChangeText={setPrice}
              placeholder="לדוגמה: 25 ש״ח"
              keyboardType="numeric"
              error={!price ? 'שדה חובה' : undefined}
            />
          </View>

          <View style={styles.inputGroup}>
            <Input
              label="פרטי איסוף"
              value={pickupDetails}
              onChangeText={setPickupDetails}
              placeholder="היכן ומתי ניתן לאסוף את המוצר?"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Input
              label="פרטי התקשרות"
              value={contactInfo}
              onChangeText={setContactInfo}
              placeholder="טלפון או אימייל ליצירת קשר"
              error={!contactInfo ? 'שדה חובה' : undefined}
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'שולח...' : 'פרסם מוצר'}
            </Text>
          </TouchableOpacity>
        </View>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  form: {
    gap: getSpacing('lg'),
  },
  inputGroup: {
    gap: getSpacing('xs'),
  },
  imageUploadContainer: {
    marginBottom: getSpacing('md'),
  },
  imageUploadButton: {
    width: '100%',
    height: 200,
    backgroundColor: getColor('neutral', 50),
    borderRadius: getBorderRadius('lg'),
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadedImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  uploadPlaceholder: {
    alignItems: 'center',
    gap: getSpacing('sm'),
  },
  uploadText: {
    ...getTypography('base'),
    color: getColor('neutral', 400),
  },
  submitButton: {
    backgroundColor: getColor('primary'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('md'),
    borderRadius: getBorderRadius('full'),
    marginTop: getSpacing('lg'),
  },
  submitButtonDisabled: {
    backgroundColor: getColor('neutral', 300),
  },
  submitButtonText: {
    ...getTypography('base', 'bold'),
    color: getColor('white'),
    textAlign: 'center',
  },
}); 