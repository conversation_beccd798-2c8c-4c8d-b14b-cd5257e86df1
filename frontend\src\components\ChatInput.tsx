import React from 'react';
import { StyleSheet } from 'react-native';
import { TextInput } from 'react-native-paper';
import { Input } from './paper/Input';

interface ChatInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  placeholder?: string;
  style?: any;
}

export const ChatInput = ({
  value,
  onChangeText,
  onSend,
  placeholder,
  style,
}: ChatInputProps) => {
  return (
    <Input
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      style={[styles.chatInput, style]}
      right={<TextInput.Icon icon="send" onPress={onSend} />}
      multiline
      numberOfLines={1}
      maxLength={500}
    />
  );
};

const styles = StyleSheet.create({
  chatInput: {
    marginVertical: 8,
  },
}); 