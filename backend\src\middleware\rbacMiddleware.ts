import { Request, Response, NextFunction } from 'express';
import { hasAccess, getRequiredRoles } from '../config/rbac';
import { getModuleLogger } from '../utils/logger';
import { AuthenticatedUser } from '../types/middleware';
import { createErrorResponse } from '../types/errors';

const logger = getModuleLogger('RBACMiddleware');

/**
 * RBAC Middleware - Enforces role-based access control
 * This middleware should be applied AFTER authentication middleware
 * It checks if the authenticated user has the required role for the requested endpoint
 */
export const rbacMiddleware = (req: Request, res: Response, next: NextFunction): void | Response => {
  try {
    // Check if user is authenticated (should be set by auth middleware)
    if (!req.user) {
      logger.warn('RBAC middleware called without authenticated user');
      return res.status(401).json(createErrorResponse('Authentication required'));
    }

    // Get user role
    const userRole = req.user.role;
    if (!userRole) {
      logger.warn('User has no role assigned', { userId: req.user.id });
      return res.status(403).json(createErrorResponse('User role not found'));
    }

    // Get request path and method
    const requestPath = req.originalUrl;
    const requestMethod = req.method;

    logger.debug('RBAC check', {
      userId: req.user.id,
      userRole,
      requestPath,
      requestMethod
    });

    // Skip role check for POST /api/users or /users
    if (req.method === 'POST' && req.originalUrl.includes('/users')) {
      logger.debug('RBAC: Skipping role check for POST /api/users or /users');
      return next();
    }

    // Check if user has access to this endpoint
    const hasPermission = hasAccess(userRole, requestPath, requestMethod);

    if (!hasPermission) {
      const requiredRoles = getRequiredRoles(requestPath, requestMethod);
      logger.warn('Access denied', {
        userId: req.user.id,
        userRole,
        requestPath,
        requestMethod,
        requiredRoles
      });

      return res.status(403).json(createErrorResponse(
        'Insufficient permissions',
        undefined,
        {
          userRole,
          requiredRoles,
          endpoint: `${requestMethod} ${requestPath}`
        }
      ));
    }

    logger.debug('Access granted', {
      userId: req.user.id,
      userRole,
      requestPath,
      requestMethod
    });

    next();
  } catch (error) {
    logger.error('RBAC middleware error:', error);
    return res.status(500).json(createErrorResponse('Internal server error during authorization'));
  }
};

/**
 * Factory function to create RBAC middleware for specific roles
 * This is an alternative approach for route-specific role requirements
 * 
 * @param allowedRoles - Array of roles that can access the endpoint
 * @returns Express middleware function
 */
export const requireRoles = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    try {
      if (!req.user) {
        logger.warn('Role check middleware called without authenticated user');
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const userRole = req.user.role;
      if (!userRole) {
        logger.warn('User has no role assigned', { userId: req.user.id });
        return res.status(403).json({
          success: false,
          error: 'User role not found'
        });
      }

      if (!allowedRoles.includes(userRole)) {
        logger.warn('Role-based access denied', {
          userId: req.user.id,
          userRole,
          allowedRoles,
          requestPath: req.path,
          requestMethod: req.method
        });

        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          details: {
            userRole,
            requiredRoles: allowedRoles,
            endpoint: `${req.method} ${req.path}`
          }
        });
      }

      logger.debug('Role-based access granted', {
        userId: req.user.id,
        userRole,
        allowedRoles,
        requestPath: req.path
      });

      next();
    } catch (error) {
      logger.error('Role check middleware error:', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error during role check'
      });
    }
  };
};

// Convenience functions for common role combinations
export const requireAdmin = () => rbacMiddleware;
export const requireModeratorOrAdmin = () => rbacMiddleware;
export const requireAnyRole = () => rbacMiddleware;
