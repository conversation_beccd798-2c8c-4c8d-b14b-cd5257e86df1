// Surveys Service - Handles survey operations
import { apiService } from './api';
import { Survey, SurveyQuestion, CreateSurveyData, SurveyResponse } from '../types/survey';

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

class SurveysService {
  // Use Custom Backend (Recommended for business logic)
  async getSurveysFromBackend(): Promise<Survey[]> {
    try {
      console.log('🔄 Fetching surveys from custom backend...');
      const response = await apiService.getSurveys();

      if (response.error) {
        throw new Error(response.error);
      }

      // Transform the data to ensure dates are Date objects and add missing fields
      const surveys = (response.data || []).map((survey: any) => ({
        ...survey,
        createdAt: new Date(survey.createdAt),
        closingDate: survey.closingDate ? new Date(survey.closingDate) : null,
        questions: survey.questions || [],
        participantsCount: survey.participantsCount || 0
      }));

      return surveys;
    } catch (error) {
      console.error('❌ Error fetching surveys from backend:', error);
      throw error;
    }
  }

  async getSurveyByIdFromBackend(surveyId: string): Promise<Survey | null> {
    try {
      console.log('🔄 Fetching survey from custom backend:', surveyId);
      const response = await apiService.getSurvey(surveyId);

      if (response.error) {
        throw new Error(response.error);
      }

      if (!response.data) {
        return null;
      }

      // Transform the data to ensure dates are Date objects
      const survey = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        closingDate: response.data.closingDate ? new Date(response.data.closingDate) : null,
        questions: response.data.questions || [],
        participantsCount: response.data.participantsCount || 0
      };

      return survey;
    } catch (error) {
      console.error('❌ Error fetching survey from backend:', error);
      throw error;
    }
  }

  async createSurveyViaBackend(surveyData: CreateSurveyData | FormData): Promise<ApiResponse<Survey>> {
    try {
      console.log('🔄 Creating survey via backend...', surveyData);

      const response = await apiService.createSurvey(surveyData);

      if (response.error) {
        return { error: response.error };
      }

      // Transform the response
      const survey = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        closingDate: response.data.closingDate ? new Date(response.data.closingDate) : null,
        questions: response.data.questions || [],
        participantsCount: response.data.participantsCount || 0
      };

      return { data: survey };
    } catch (error) {
      console.error('❌ Error creating survey via backend:', error);
      return { error: 'Failed to create survey' };
    }
  }

  // Use backend API with token authentication
  async getSurveys(): Promise<Survey[]> {
    try {
      return await this.getSurveysFromBackend();
    } catch (error) {
      console.error('❌ Failed to fetch surveys from backend:', error);
      throw new Error('Failed to fetch surveys');
    }
  }

  async getSurveyById(surveyId: string): Promise<Survey | null> {
    try {
      return await this.getSurveyByIdFromBackend(surveyId);
    } catch (error) {
      console.error('❌ Failed to fetch survey from backend:', error);
      throw new Error('Failed to fetch survey');
    }
  }

  async createSurvey(surveyData: CreateSurveyData): Promise<ApiResponse<Survey>> {
    try {
      return await this.createSurveyViaBackend(surveyData);
    } catch (error) {
      console.error('❌ Failed to create survey:', error);
      return { error: 'Failed to create survey' };
    }
  }

  async submitSurveyResponse(surveyId: string, answers: { [questionId: string]: any }): Promise<ApiResponse<SurveyResponse>> {
    try {
      console.log('🔄 Submitting survey response...', { surveyId, answers });

      // Send just the answers in the request body
      const responseData = {
        answers
      };

      const response = await apiService.submitSurveyResponse(surveyId, responseData);

      console.log('🔍 Survey response from API:', {
        hasError: !!response.error,
        error: response.error,
        data: response.data,
        status: response.status
      });

      if (response.error) {
        // Handle specific error codes
        if (response.data?.code === 'DUPLICATE_RESPONSE') {
          return { error: 'כבר הגבתם לסקר זה. כל משתמש יכול להגיב רק פעם אחת.' };
        } else if (response.data?.code === 'SURVEY_CLOSED') {
          return { error: 'הסקר נסגר ואינו מקבל תשובות נוספות.' };
        } else if (response.data?.code === 'SURVEY_NOT_FOUND') {
          return { error: 'הסקר לא נמצא.' };
        } else if (response.data?.code === 'UNAUTHORIZED') {
          return { error: 'נדרשת התחברות למערכת.' };
        }
        return { error: response.error };
      }

      return { data: response.data };
    } catch (error) {
      console.error('❌ Error submitting survey response:', error);
      return { error: 'Failed to submit survey response' };
    }
  }

  // Real-time subscriptions (only available via Supabase)
  subscribeToSurveys(callback: (surveys: Survey[]) => void) {
    console.log('🔄 Setting up real-time survey subscription...');
    
    const subscription = supabase
      .channel('surveys')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'Survey' },
        (payload) => {
          console.log('📡 Real-time survey update:', payload);
          // Refetch surveys when changes occur
          this.getSurveys().then(callback).catch(console.error);
        }
      )
      .subscribe();

    return () => {
      console.log('🔄 Unsubscribing from surveys...');
      subscription.unsubscribe();
    };
  }
}

export const surveysService = new SurveysService();
export default surveysService;
