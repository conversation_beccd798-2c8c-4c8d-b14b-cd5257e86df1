import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
} from 'react-native';

interface SimpleCalendarProps {
  visible: boolean;
  onClose: () => void;
  onDateSelect: (date: Date) => void;
  selectedDate?: Date;
  minimumDate?: Date;
  title?: string;
}

export const SimpleCalendar: React.FC<SimpleCalendarProps> = ({
  visible,
  onClose,
  onDateSelect,
  selectedDate,
  minimumDate = new Date(),
  title = 'בחר תאריך',
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [tempSelectedDate, setTempSelectedDate] = useState<Date | null>(
    selectedDate || null
  );

  const monthNames = [
    'ינואר', 'פברואר', 'מרץ', 'אפריל', 'מאי', 'יוני',
    'יולי', 'אוגוסט', 'ספטמבר', 'אוקטובר', 'נובמבר', 'דצמבר'
  ];

  const dayNames = ['א', 'ב', 'ג', 'ד', 'ה', 'ו', 'ש'];

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    // Add empty cells after the last day to complete the last week
    while (days.length % 7 !== 0) {
      days.push(null);
    }

    return days;
  };

  const isDateDisabled = (date: Date) => {
    return date < minimumDate;
  };

  const isDateSelected = (date: Date) => {
    if (!tempSelectedDate) return false;
    return (
      date.getDate() === tempSelectedDate.getDate() &&
      date.getMonth() === tempSelectedDate.getMonth() &&
      date.getFullYear() === tempSelectedDate.getFullYear()
    );
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const handleDatePress = (date: Date) => {
    if (!isDateDisabled(date)) {
      setTempSelectedDate(date);
    }
  };

  const handlePrevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  const handleConfirm = () => {
    if (tempSelectedDate) {
      onDateSelect(tempSelectedDate);
    }
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedDate(selectedDate || null);
    onClose();
  };

  const days = getDaysInMonth(currentMonth);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{title}</Text>
          
          {/* Month/Year Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handlePrevMonth} style={styles.navButton}>
              <Text style={styles.navButtonText}>◀</Text>
            </TouchableOpacity>
            
            <Text style={styles.monthYear}>
              {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
            </Text>
            
            <TouchableOpacity onPress={handleNextMonth} style={styles.navButton}>
              <Text style={styles.navButtonText}>▶</Text>
            </TouchableOpacity>
          </View>

          {/* Day Names */}
          <View style={styles.dayNamesRow}>
            {dayNames.map((dayName, index) => (
              <Text key={index} style={styles.dayName}>
                {dayName}
              </Text>
            ))}
          </View>

          {/* Calendar Grid */}
          <View style={styles.calendar}>
            {Array.from({ length: Math.ceil(days.length / 7) }, (_, weekIndex) => (
              <View key={weekIndex} style={styles.week}>
                {days.slice(weekIndex * 7, (weekIndex + 1) * 7).map((date, dayIndex) => (
                  <TouchableOpacity
                    key={dayIndex}
                    style={[
                      styles.day,
                      date && isToday(date) && styles.today,
                      date && isDateSelected(date) && styles.selectedDay,
                      date && isDateDisabled(date) && styles.disabledDay,
                    ]}
                    onPress={() => date && handleDatePress(date)}
                    disabled={!date || isDateDisabled(date)}
                  >
                    <Text
                      style={[
                        styles.dayText,
                        date && isToday(date) && styles.todayText,
                        date && isDateSelected(date) && styles.selectedDayText,
                        date && isDateDisabled(date) && styles.disabledDayText,
                      ]}
                    >
                      {date ? date.getDate() : ''}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </View>

          {/* Action Buttons */}
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleConfirm}
            >
              <Text style={styles.modalButtonText}>אישור</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCancelButton]}
              onPress={handleCancel}
            >
              <Text style={styles.modalCancelButtonText}>ביטול</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxWidth: 350,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#2c3e50',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 15,
  },
  navButton: {
    padding: 10,
  },
  navButtonText: {
    fontSize: 18,
    color: '#667eea',
    fontWeight: 'bold',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  monthYear: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  dayNamesRow: {
    flexDirection: 'row',
    width: '100%',
    marginBottom: 10,
  },
  dayName: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
    color: '#7f8c8d',
    paddingVertical: 5,
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  calendar: {
    width: '100%',
    marginBottom: 20,
  },
  week: {
    flexDirection: 'row',
  },
  day: {
    flex: 1,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 1,
    borderRadius: 5,
  },
  today: {
    backgroundColor: '#f0f8ff',
  },
  selectedDay: {
    backgroundColor: '#667eea',
  },
  disabledDay: {
    backgroundColor: '#f8f9fa',
  },
  dayText: {
    fontSize: 14,
    color: '#2c3e50',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  todayText: {
    color: '#667eea',
    fontWeight: 'bold',
  },
  selectedDayText: {
    color: 'white',
    fontWeight: 'bold',
  },
  disabledDayText: {
    color: '#bdc3c7',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  modalButton: {
    backgroundColor: '#667eea',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 80,
  },
  modalButtonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 16,
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  modalCancelButton: {
    backgroundColor: '#ecf0f1',
  },
  modalCancelButtonText: {
    color: '#7f8c8d',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 16,
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
});
