import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { SearchInput } from '../../components/SearchInput';

import { HEADER_ICONS } from '@constants';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme/utils';
import { SurveysStackParamList } from '@navigation/types';
import { Survey } from '../../types/survey';
import { apiService } from '@services/api';
import { useAuth } from '@contexts/AuthContext';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';


type SurveysListScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'SurveysList'>;

interface SurveyCardProps {
  survey: Survey;
  onPress: () => void;
}

export default function SurveysListScreen() {
  const navigation = useNavigation<SurveysListScreenNavigationProp>();
  const { user } = useAuth();
  const [surveys, setSurveys] = useState<Survey[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const loadSurveys = async () => {
    try {
      console.log('🔄 Loading surveys...');
      const response = await apiService.getSurveys();
      if (response.data) {
        setSurveys(response.data);
        console.log('✅ Surveys loaded successfully:', response.data.length);
      } else {
        console.error('❌ Error loading surveys:', response.error);
      }
    } catch (error) {
      console.error('❌ Error loading surveys:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadSurveys();
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadSurveys();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadSurveys();
  };

  const handleAddSurvey = () => {
    navigation.navigate('CreateSurvey');
  };

  const handleSurveyPress = (surveyId: string) => {
    navigation.navigate('SurveyDetails', { surveyId });
  };

  const filteredSurveys = surveys.filter(survey =>
    survey.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    survey.description.toLowerCase().includes(searchQuery.toLowerCase())
  );



  const SurveyCard: React.FC<SurveyCardProps> = ({ survey, onPress }) => {
    const isOpen = survey.status === 'OPEN';

    return (
      <TouchableOpacity
        style={styles.marketCard}
        onPress={onPress}
      >
        <View style={styles.cardIcon}>
          <Text style={styles.cardIconText}>🏗️</Text>
        </View>
        <View style={styles.cardText}>
          <Text style={styles.cardTitle}>{survey.title}</Text>
          <Text style={styles.cardDescription}>{survey.description}</Text>
        </View>
        <View style={[styles.priceTag, { backgroundColor: isOpen ? getColor('success') : getColor('neutral', 500) }]}>
          <Text style={styles.priceTagText}>
            {isOpen ? 'סקר פתוח' : 'סקר סגור'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Add button for all users (matching mockup design)
  const addButton = (
    <TouchableOpacity
      style={styles.headerButton}
      onPress={handleAddSurvey}
    >
      <Text style={styles.headerButtonText}>חדש</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען סקרים...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="📝 סקרים"
        showBackButton={true}
        rightComponent={addButton}
      />

      <View style={styles.content}>
        <SearchInput
          placeholder="חיפוש סקרים..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        <FlatList
          data={filteredSurveys}
          renderItem={({ item }) => (
            <SurveyCard survey={item} onPress={() => handleSurveyPress(item.id)} />
          )}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <TouchableOpacity style={styles.fab} onPress={handleAddSurvey}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 50),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: getSpacing('lg'),
  },
  listContainer: {
    paddingBottom: 100,
  },
  marketCard: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    flexDirection: 'row',
    alignItems: 'center',
    ...getShadow('sm'),
  },
  cardIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getSpacing('md'),
  },
  cardIconText: {
    fontSize: 24,
  },
  cardText: {
    flex: 1,
    marginRight: getSpacing('md'),
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: '#7f8c8d',
    lineHeight: 20,
  },
  priceTag: {
    paddingHorizontal: getSpacing('sm'),
    paddingVertical: getSpacing('xs'),
    borderRadius: getBorderRadius('sm'),
    minWidth: 80,
    alignItems: 'center',
  },
  priceTagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  headerButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: getBorderRadius('sm'),
    padding: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getSpacing('lg'),
  },
  errorText: {
    ...getTypography('base', 'normal'),
    color: getColor('error'),
    textAlign: 'center',
    marginBottom: getSpacing('md'),
  },
  retryButton: {
    backgroundColor: getColor('primary'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('md'),
    borderRadius: getBorderRadius('md'),
  },
  retryButtonText: {
    ...getTypography('base', 'medium'),
    color: getColor('white'),
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('md'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  fabText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
});
