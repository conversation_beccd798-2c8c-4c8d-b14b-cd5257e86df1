import { logger } from '../utils/logger';
import { Client } from 'minio';
import { minioConfig } from '../config/minio';

// Bucket name constants
export const BUCKETS = {
  FOOD_IMAGES: 'food-images',
  EVENT_IMAGES: 'event-images',
  SURVEY_IMAGES: 'survey-images',
} as const;

export type BucketName = typeof BUCKETS[keyof typeof BUCKETS];

export interface BucketConfig {
  name: string;
  public: boolean;
  fileSizeLimit?: number;
  allowedMimeTypes?: string[];
}

export interface BucketResponse {
  name: string;
  creationDate: Date;
}

export interface UploadOptions {
  contentType?: string;
}

export interface UploadResponse {
  path: string;
  fullPath: string;
}

export interface FileObject {
  name: string;
  lastModified: Date;
  size: number;
  etag: string;
}

export interface ListFilesOptions {
  prefix?: string;
  recursive?: boolean;
}

export interface ListFilesResponse {
  data: FileObject[] | null;
  error: any;
}

export interface StorageFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
  destination?: string;
  filename?: string;
  path?: string;
}

export class MinioStorageClient {
  private static instance: MinioStorageClient;
  private minioClient: Client;
  private initialized = false;
  private readonly buckets: BucketConfig[] = [
    {
      name: 'food-images',
      public: false,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif']
    },
    {
      name: 'event-images',
      public: false,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif']
    },
    {
      name: 'survey-images',
      public: false,
      fileSizeLimit: 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif']
    }
  ];

  private constructor() {
    this.minioClient = new Client({
      endPoint: minioConfig.endpoint,
      port: minioConfig.port,
      useSSL: minioConfig.useSSL,
      accessKey: minioConfig.accessKey,
      secretKey: minioConfig.secretKey
    });
  }

  public static getInstance(): MinioStorageClient {
    if (!MinioStorageClient.instance) {
      MinioStorageClient.instance = new MinioStorageClient();
    }
    return MinioStorageClient.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.info('MinIO storage service already initialized');
      return;
    }

    try {
      logger.info('Starting MinIO storage service initialization...');
      // Create buckets if they don't exist
      for (const bucket of this.buckets) {
        logger.info(`Initializing bucket: ${bucket.name}`);
        await this.createBucket(bucket);
      }
      this.initialized = true;
      logger.info('MinIO storage service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize MinIO storage service:', error);
      throw error;
    }
  }

  public async createBucket(config: BucketConfig): Promise<{ data: BucketResponse | null; error: any }> {
    try {
      logger.info(`Checking if bucket '${config.name}' exists...`);
      const exists = await this.minioClient.bucketExists(config.name);

      if (!exists) {
        logger.info(`Creating bucket '${config.name}'...`);
        await this.minioClient.makeBucket(config.name, 'us-east-1');
        
        // Set bucket policy if public
        if (config.public) {
          const policy = {
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Principal: { AWS: ['*'] },
                Action: ['s3:GetObject'],
                Resource: [`arn:aws:s3:::${config.name}/*`]
              }
            ]
          };
          await this.minioClient.setBucketPolicy(config.name, JSON.stringify(policy));
        }

        logger.info(`Successfully created bucket: ${config.name}`);
      } else {
        logger.info(`Bucket '${config.name}' already exists`);
      }

      return {
        data: {
          name: config.name,
          creationDate: new Date()
        },
        error: null
      };
    } catch (error) {
      logger.error(`Error managing bucket '${config.name}':`, error);
      return { data: null, error };
    }
  }

  public async uploadFile(
    bucketName: string,
    filePath: string,
    file: Buffer,
    options: UploadOptions = {}
  ): Promise<{ data: UploadResponse | null; error: any }> {
    try {
      await this.validateBucket(bucketName);

      await this.minioClient.putObject(
        bucketName,
        filePath,
        file,
        file.length,
        options.contentType ? { 'Content-Type': options.contentType } : undefined
      );

      return {
        data: {
          path: filePath,
          fullPath: `${bucketName}/${filePath}`
        },
        error: null
      };
    } catch (error) {
      logger.error(`Error uploading file to '${filePath}' in bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public async uploadImage(file: StorageFile, bucketName: string): Promise<string> {
    try {
      logger.info('[MINIO] Starting image upload', {
        bucket: bucketName,
        filename: file.filename,
        mimetype: file.mimetype,
        size: file.size
      });

      await this.validateBucket(bucketName);
      logger.info('[MINIO] Bucket validation successful', { bucket: bucketName });

      const filePath = `${file.fieldname}/${file.filename}`;
      logger.info('[MINIO] Uploading file to path', { path: filePath });

      await this.minioClient.putObject(
        bucketName,
        filePath,
        file.buffer,
        file.size,
        { 'Content-Type': file.mimetype }
      );

      const publicUrl = this.getPublicUrl(bucketName, filePath);
      logger.info('[MINIO] Image upload successful', {
        bucket: bucketName,
        path: filePath,
        publicUrl: publicUrl
      });

      return publicUrl;
    } catch (error) {
      logger.error('[MINIO] Error uploading image:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        bucket: bucketName,
        filename: file.filename
      });
      throw error;
    }
  }

  public async deleteImage(imageUrl: string, bucket: BucketName): Promise<void> {
    try {
      const path = imageUrl.split('/').pop();
      if (!path) {
        throw new Error('Invalid image URL');
      }

      await this.minioClient.removeObject(bucket, path);
    } catch (error) {
      logger.error(`Error deleting image from bucket '${bucket}':`, error);
      throw error;
    }
  }

  public async getImage(imageUrl: string, bucket: BucketName): Promise<string> {
    try {
      const path = imageUrl.split('/').pop();
      if (!path) {
        throw new Error('Invalid image URL');
      }

      return await this.minioClient.presignedGetObject(bucket, path, 3600);
    } catch (error) {
      logger.error(`Error getting image from bucket '${bucket}':`, error);
      throw error;
    }
  }

  public getPublicUrl(bucket: string, path: string): string {
    return `${minioConfig.endpoint}:${minioConfig.port}/${bucket}/${path}`;
  }

  private async validateBucket(bucketName: string): Promise<void> {
    const exists = await this.minioClient.bucketExists(bucketName);
    if (!exists) {
      throw new Error(`Bucket '${bucketName}' does not exist or is not accessible`);
    }
  }

  public async listFiles(
    bucketName: string,
    options: ListFilesOptions = {}
  ): Promise<ListFilesResponse> {
    try {
      logger.info(`Listing files in bucket '${bucketName}'...`);
      await this.validateBucket(bucketName);

      const files: FileObject[] = [];
      const stream = this.minioClient.listObjects(bucketName, options.prefix || '', options.recursive || false);

      for await (const obj of stream) {
        if (obj.name && obj.size !== undefined) {  // Only add valid file objects
          files.push({
            name: obj.name,
            lastModified: obj.lastModified,
            size: obj.size,
            etag: obj.etag
          });
        }
      }

      return {
        data: files,
        error: null
      };
    } catch (error) {
      logger.error(`Error listing files in bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public async deleteFiles(
    bucketName: string,
    filePaths: string[]
  ): Promise<{ data: any[] | null; error: any }> {
    try {
      await this.validateBucket(bucketName);

      const errors: any[] = [];
      for (const path of filePaths) {
        try {
          await this.minioClient.removeObject(bucketName, path);
        } catch (error) {
          errors.push({ path, error });
        }
      }

      return { data: errors.length > 0 ? errors : null, error: null };
    } catch (error) {
      logger.error(`Error deleting files from bucket '${bucketName}':`, error);
      return { data: null, error };
    }
  }

  public getBucketConfig(bucketName: string): BucketConfig {
    const bucket = this.buckets.find(b => b.name === bucketName);
    if (!bucket) {
      throw new Error(`Bucket '${bucketName}' configuration not found`);
    }
    return bucket;
  }

  public async getImageData(imageUrl: string, bucket: BucketName): Promise<{ data: Buffer; contentType: string }> {
    try {
      // Extract the path after the bucket name
      const urlParts = imageUrl.split('/');
      const bucketIndex = urlParts.indexOf(bucket);
      if (bucketIndex === -1) {
        throw new Error('Invalid image URL: bucket name not found');
      }
      const path = urlParts.slice(bucketIndex + 1).join('/');
      if (!path) {
        throw new Error('Invalid image URL: no path found');
      }

      const stream = await this.minioClient.getObject(bucket, path);
      const chunks: Buffer[] = [];
      
      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => {
          const data = Buffer.concat(chunks);
          // Default to image/jpeg if content type is not available
          const contentType = 'image/jpeg';
          resolve({ data, contentType });
        });
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error(`Error getting image data from bucket '${bucket}':`, error);
      throw error;
    }
  }
}

export const minioStorageClient = MinioStorageClient.getInstance(); 