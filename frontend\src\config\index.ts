import { Platform } from 'react-native';

// Platform-specific URL configuration
const getBaseUrl = (port: number) => {
  return Platform.select({
    android: `http://********:${port}`, // Android emulator uses ******** to access host machine
    ios: `http://localhost:${port}`,     // iOS simulator can use localhost
    default: `http://localhost:${port}`, // Web and other platforms use localhost
  });
};

// API Configuration
// Handle both web (REACT_APP_) and mobile (EXPO_PUBLIC_) environment variables
const getApiUrl = () => {
  // Check for Expo/React Native environment first
  if (process.env.EXPO_PUBLIC_API_URL) {
    console.log('🔧 Using EXPO_PUBLIC_API_URL:', process.env.EXPO_PUBLIC_API_URL);
    return process.env.EXPO_PUBLIC_API_URL;
  }
  // Fall back to React web environment
  if (process.env.REACT_APP_API_URL) {
    console.log('🔧 Using REACT_APP_API_URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }
  // Default fallback
  console.warn('⚠️ No API URL environment variable found! Using default:', 'http://localhost:3002/api');
  console.warn('⚠️ To set custom API URL, add EXPO_PUBLIC_API_URL to your .env file');
  return 'http://localhost:3002/api';
};

// Supabase Configuration
const getSupabaseConfig = () => {
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || getBaseUrl(8000);
  const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn('⚠️ Supabase configuration missing!');
    console.warn('⚠️ Please add the following to your .env file:');
    console.warn('⚠️ EXPO_PUBLIC_SUPABASE_URL=your_supabase_url');
    console.warn('⚠️ EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key');
    return {
      url: 'https://your-project.supabase.co',
      anonKey: 'your-anon-key'
    };
  }

  console.log('🔧 Using Supabase URL:', supabaseUrl);
  return {
    url: supabaseUrl,
    anonKey: supabaseAnonKey
  };
};

export const API_URL = getApiUrl();
export const SUPABASE_CONFIG = getSupabaseConfig();

// Main configuration object
export const APP_CONFIG = {
  API_URL,
  SUPABASE_CONFIG,
  // Add other configuration values as needed
}; 