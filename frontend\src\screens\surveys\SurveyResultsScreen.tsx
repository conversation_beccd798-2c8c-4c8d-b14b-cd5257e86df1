import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView } from '@components';
import { SurveysStackParamList } from '@navigation/types';
import { surveysService } from '@services/surveys';
import { Survey, SurveyQuestion } from '../../types/survey';
import { Input } from '../../components/paper/Input';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';

type SurveyResultsScreenRouteProp = RouteProp<SurveysStackParamList, 'SurveyResults'>;
type SurveyResultsScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'SurveyResults'>;

export default function AnswerSurveyScreen() {
  const route = useRoute<SurveyResultsScreenRouteProp>();
  const navigation = useNavigation<SurveyResultsScreenNavigationProp>();
  const { surveyId } = route.params;

  const [survey, setSurvey] = useState<Survey | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [answers, setAnswers] = useState<{ [questionId: string]: any }>({});
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    loadSurvey();
  }, [surveyId]);

  const loadSurvey = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading survey for answering:', surveyId);
      const surveyData = await surveysService.getSurveyById(surveyId);

      if (surveyData) {
        setSurvey(surveyData);
        console.log('✅ Survey loaded for answering:', surveyData.title);
      } else {
        Alert.alert('שגיאה', 'הסקר לא נמצא');
      }
    } catch (error) {
      console.error('❌ Error loading survey:', error);
      Alert.alert('שגיאה', 'שגיאה בטעינת הסקר');
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerChange = (questionId: string, answer: any) => {
    console.log('📝 Answer changed:', {
      questionId,
      answer,
      previousAnswer: answers[questionId]
    });
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      console.log('🔄 Submitting survey responses:', {
        surveyId,
        answersCount: Object.keys(answers).length,
        answers
      });

      const response = await surveysService.submitSurveyResponse(surveyId, answers);

      console.log('🔍 Survey submission response:', {
        hasData: !!response.data,
        hasError: !!response.error,
        error: response.error,
        data: response.data
      });

      if (response.data) {
        console.log('✅ Survey responses submitted successfully:', {
          surveyId,
          responseId: response.data.id
        });
        setSuccessMessage('התשובות נשלחו בהצלחה. תודה על השתתפותכם!');
        setErrorMessage(null);

        // Navigate back after a short delay to show success message
        setTimeout(() => {
          navigation.navigate('SurveysList' as never);
        }, 2000);
      } else {
        console.error('❌ Failed to submit survey responses:', response.error);
        console.log('🚨 Setting error message:', response.error);

        setErrorMessage(response.error || 'שגיאה בשליחת התשובות');
        setSuccessMessage(null);

        // Navigate back after showing error for a few seconds
        setTimeout(() => {
          navigation.navigate('SurveysList' as never);
        }, 3000);
      }
    } catch (error) {
      console.error('❌ Error submitting survey responses:', error);
      setErrorMessage('אירעה שגיאה בשליחת התשובות. אנא נסו שוב.');
      setSuccessMessage(null);

      // Navigate back after showing error
      setTimeout(() => {
        navigation.navigate('SurveysList' as never);
      }, 3000);
    } finally {
      setSubmitting(false);
    }
  };

  const renderQuestion = (question: SurveyQuestion) => {
    switch (question.type) {
      case 'single-choice':
        return (
          <View key={question.id} style={styles.questionContainer}>
            <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
              {question.text}
            </Text>
            {question.options?.map((option, optionIndex) => (
              <TouchableOpacity
                key={optionIndex}
                style={styles.optionContainer}
                onPress={() => handleAnswerChange(question.id, option)}
              >
                <View style={styles.radioButton}>
                  {answers[question.id] === option && (
                    <View style={styles.radioButtonSelected} />
                  )}
                </View>
                <Text style={[styles.optionText, { textAlign: getTextAlign() }]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );

      case 'multiple-choice':
        return (
          <View key={question.id} style={styles.questionContainer}>
            <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
              {question.text}
            </Text>
            {question.options?.map((option, optionIndex) => {
              const selectedOptions = answers[question.id] || [];
              const isSelected = selectedOptions.includes(option);

              return (
                <TouchableOpacity
                  key={optionIndex}
                  style={styles.optionContainer}
                  onPress={() => {
                    const currentAnswers = answers[question.id] || [];
                    let newAnswers;
                    if (isSelected) {
                      newAnswers = currentAnswers.filter((a: string) => a !== option);
                    } else {
                      newAnswers = [...currentAnswers, option];
                    }
                    handleAnswerChange(question.id, newAnswers);
                  }}
                >
                  <View style={styles.checkbox}>
                    {isSelected && (
                      <Text style={styles.checkmark}>✓</Text>
                    )}
                  </View>
                  <Text style={[styles.optionText, { textAlign: getTextAlign() }]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        );

      case 'text':
        return (
          <View key={question.id} style={styles.questionContainer}>
            <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
              {question.text}
            </Text>
            <Input
              value={answers[question.id] || ''}
              onChangeText={(text: string) => handleAnswerChange(question.id, text)}
              placeholder="הקלד את תשובתך כאן..."
              multiline
              numberOfLines={4}
            />
          </View>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען סקר...</Text>
      </View>
    );
  }

  if (!survey) {
    return (
      <View style={styles.centerContainer}>
        <Text>הסקר לא נמצא</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="📋 ענו על הסקר"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <Text style={[styles.surveyTitle, { textAlign: getTextAlign() }]}>
          {survey.title}
        </Text>

        {/* Success Message */}
        {successMessage && (
          <View style={styles.successContainer}>
            <Text style={[styles.successText, { textAlign: getTextAlign() }]}>
              ✅ {successMessage}
            </Text>
          </View>
        )}

        {/* Error Message */}
        {errorMessage && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { textAlign: getTextAlign() }]}>
              ❌ {errorMessage}
            </Text>
          </View>
        )}

        {survey.questions.map((question) => renderQuestion(question))}

        {/* Additional Comments */}
        <Text style={[styles.questionText, { textAlign: getTextAlign() }]}>
          הערות נוספות
        </Text>
        <Input
          value={answers['comments'] || ''}
          onChangeText={(text: string) => handleAnswerChange('comments', text)}
          placeholder="הקלד את הערותך כאן..."
          multiline
          numberOfLines={4}
        />

        <TouchableOpacity
          style={[styles.submitButton, submitting && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={submitting}
        >
          <Text style={[styles.submitButtonText, { textAlign: getTextAlign() }]}>
            {submitting ? 'שולח תשובות...' : 'שלח תשובות'}
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  surveyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 20,
  },
  questionContainer: {
    marginBottom: getSpacing('lg'),
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    ...getShadow('sm'),
  },
  questionText: {
    ...getTypography('base', 'bold'),
    color: getColor('neutral', 700),
    marginBottom: getSpacing('md'),
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing('sm'),
    padding: getSpacing('sm'),
    backgroundColor: getColor('neutral', 50),
    borderRadius: getBorderRadius('md'),
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: getColor('primary'),
    marginRight: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: getColor('primary'),
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: getColor('primary'),
    marginRight: getSpacing('sm'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: getColor('primary'),
    fontSize: 14,
    fontWeight: 'bold',
  },
  optionText: {
    ...getTypography('base'),
    color: getColor('neutral', 700),
    flex: 1,
  },
  submitButton: {
    backgroundColor: getColor('primary'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('md'),
    borderRadius: getBorderRadius('full'),
    marginTop: getSpacing('lg'),
  },
  submitButtonDisabled: {
    backgroundColor: getColor('neutral', 300),
  },
  submitButtonText: {
    ...getTypography('base', 'bold'),
    color: getColor('white'),
    textAlign: 'center',
  },
  successContainer: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
    borderWidth: 1,
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  successText: {
    color: '#155724',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
    borderWidth: 1,
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  errorText: {
    color: '#721c24',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
