import { Request, Response } from 'express';
import { SurveyService } from './survey.service';
import { logger } from '../../utils/logger';
import { Survey } from './survey.types';

export class SurveyController {
  private surveyService: SurveyService;

  constructor() {
    this.surveyService = new SurveyService();
  }

  async getSurveys(_req: Request, res: Response) {
    try {
      const surveys = await this.surveyService.getSurveys();
      return res.json(surveys);
    } catch (error) {
      return res.status(500).json({ message: 'Error fetching surveys', error });
    }
  }

  public createSurvey = async (req: Request, res: Response) => {
    try {
      logger.info('[SURVEY] Creating new survey - Request details:', {
        hasFile: !!req.file,
        fileDetails: req.file ? {
          fieldname: req.file.fieldname,
          originalname: req.file.originalname,
          encoding: req.file.encoding,
          mimetype: req.file.mimetype,
          size: req.file.size,
          buffer: `<PERSON>uff<PERSON>(${req.file.buffer.length} bytes)`
        } : null,
        body: req.body,
        headers: {
          'content-type': req.headers['content-type'],
          'content-length': req.headers['content-length']
        }
      });

      const { title, description, questions, endDate } = req.body;
      
      // Create survey data object
      const surveyData: Omit<Survey, 'id'> = {
        title,
        description,
        questions: JSON.parse(questions),
        status: 'OPEN',
        createdBy: req.user?.id || 'default',
        communityId: '1', // TODO: Get from user's community
        participantsCount: 0,
        closingDate: endDate ? new Date(endDate) : null,
        imageUrl: null,
        dataAiHint: null,
        createdAt: new Date()
      };

      let imageFile = req.file;

      const survey = await this.surveyService.createSurvey(surveyData, imageFile);
      logger.info('[SURVEY] Survey created successfully', {
        id: survey.id,
        title: survey.title,
        has_image: !!survey.imageUrl,
        image_url: survey.imageUrl
      });

      res.status(201).json(survey);
    } catch (error) {
      logger.error('[SURVEY] Error creating survey:', error);
      res.status(500).json({ error: 'Failed to create survey' });
    }
  };

  async getSurvey(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({
          message: 'Survey ID is required',
          code: 'MISSING_SURVEY_ID',
          success: false
        });
      }
      const survey = await this.surveyService.getSurvey(id as string);
      if (!survey) {
        return res.status(404).json({
          message: 'Survey not found',
          code: 'SURVEY_NOT_FOUND',
          success: false
        });
      }
      return res.json({ ...survey, success: true });
    } catch (error) {
      console.error('❌ Error fetching survey:', error);
      return res.status(500).json({
        message: 'Error fetching survey',
        code: 'INTERNAL_ERROR',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  async updateSurvey(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }
      const updatedSurvey = await this.surveyService.updateSurvey(id as string, req.body);
      if (!updatedSurvey) {
        return res.status(404).json({ message: 'Survey not found' });
      }
      return res.json(updatedSurvey);
    } catch (error) {
      return res.status(500).json({ message: 'Error updating survey', error });
    }
  }

  async deleteSurvey(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }
      await this.surveyService.deleteSurvey(id as string);
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ message: 'Error deleting survey', error });
    }
  }

  async submitSurveyResponse(req: Request, res: Response) {
    try {
      const surveyId = req.params['id'];
      const userId = (req as any).user?.id;

      if (!surveyId) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }

      if (!userId) {
        return res.status(401).json({ message: 'User authentication required' });
      }

      console.log('📝 Survey response request:', {
        surveyId,
        userId,
        body: req.body,
        headers: req.headers.authorization ? 'Present' : 'Missing'
      });

      const responseData = {
        surveyId,
        userId,
        answers: req.body.answers,
        submittedAt: new Date()
      };

      const response = await this.surveyService.submitSurveyResponse(responseData);
      return res.status(201).json(response);
    } catch (error) {
      console.error('❌ Error submitting survey response:', error);

      if (error instanceof Error) {
        const statusCode = (error as any).statusCode || 500;
        const errorCode = (error as any).code || 'INTERNAL_ERROR';

        return res.status(statusCode).json({
          message: error.message,
          code: errorCode,
          success: false
        });
      }

      return res.status(500).json({
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
        success: false
      });
    }
  }

  async getSurveyResponses(req: Request, res: Response) {
    try {
      const surveyId = req.params['id'];

      if (!surveyId) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }

      const responses = await this.surveyService.getSurveyResponses(surveyId);
      return res.json(responses);
    } catch (error) {
      return res.status(500).json({ message: 'Error fetching survey responses', error });
    }
  }
}