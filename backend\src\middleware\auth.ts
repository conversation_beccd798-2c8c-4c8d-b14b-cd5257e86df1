import { Request, Response, NextFunction } from 'express';
import { getServiceRoleClient } from '../config/supabase';
import { getModuleLogger } from '../utils/logger';
import prisma from '../config/prismaClient';
import { AuthenticatedUser } from '../types/middleware';
import { createErrorResponse } from '../types/errors';

const logger = getModuleLogger('AuthMiddleware');

const isPublicRoute = (path: string): boolean => {
  return path.startsWith('/api/auth/') || path === '/api/health';
};

export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    logger.debug('Auth middleware started', {
      path: req.path,
      originalUrl: req.originalUrl,
      method: req.method,
      headers: req.headers
    });

    // Skip auth for public routes
    if (isPublicRoute(req.path)) {
      logger.debug('Skipping auth for public route', { path: req.path });
      return next();
    }

    const authHeader = req.headers.authorization;
    if (!authHeader) {
      logger.warn('No authorization header found');
      return res.status(401).json(createErrorResponse('No authorization header'));
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      logger.warn('No token found in authorization header');
      return res.status(401).json(createErrorResponse('No token provided'));
    }

    logger.debug('Verifying token with Supabase', { token: token.substring(0, 10) + '...' });

    // Verify token with Supabase
    const supabase = getServiceRoleClient();
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) {
      logger.error('Token verification failed', { error: error.message });
      return res.status(401).json(createErrorResponse('Invalid token'));
    }

    if (!user) {
      logger.error('No user found in token verification');
      return res.status(401).json(createErrorResponse('User not found in token'));
    }

    logger.debug('Token verified successfully', { 
      userId: user.id,
      email: user.email || 'no-email'
    });

    // Special case for POST /api/users - skip user existence check
    if (req.method === 'POST' && req.originalUrl.includes('/users')) {
      logger.debug('Skipping user existence check for POST /api/users or /users');
      req.user = {
        id: user.id,
        email: user.email || 'no-email',
        role: 'USER', // Default role for new users
        user_type: 'ADULT' // Default user type
      };
      return next();
    }

    // Check if user exists in database
    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        role: true,
        user_type: true,
        first_name: true,
        last_name: true
      }
    });

    if (!dbUser) {
      logger.warn('User not found in database', { userId: user.id });
      return res.status(401).json(createErrorResponse('User not found in database'));
    }

    logger.debug('User found in database', { 
      userId: dbUser.id,
      role: dbUser.role,
      user_type: dbUser.user_type
    });

    // Attach user to request
    req.user = {
      id: dbUser.id,
      email: dbUser.email,
      role: dbUser.role,
      user_type: dbUser.user_type,
      first_name: dbUser.first_name,
      last_name: dbUser.last_name
    };

    next();
  } catch (error) {
    logger.error('Error in auth middleware', { 
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    res.status(500).json(createErrorResponse('Internal server error'));
  }
};

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
    }
  }
}