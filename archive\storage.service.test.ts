/*import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { storageClient } from '../storage.service';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('StorageTest');

describe('Storage Service - Real Tests', () => {
  const TEST_BUCKET = 'test-bucket';
  const TEST_FILE_PATH = 'test-folder/test-file.txt';
  const TEST_FILE_CONTENT = 'Hello, this is a test file!';
  let testFileBuffer: Buffer;

  beforeAll(async () => {
    testFileBuffer = Buffer.from(TEST_FILE_CONTENT);
    // Initialize storage client
    await storageClient.initialize();
  });

  afterAll(async () => {
    try {
      // Cleanup: Delete the test bucket
      await storageClient.deleteBucket(TEST_BUCKET);
      logger.info('Test cleanup completed');
    } catch (error) {
      logger.error('Error during test cleanup:', error);
    }
  });

  it('should perform a complete storage lifecycle', async () => {
    logger.info('Starting Storage Lifecycle Test');

    // 1. Create bucket
    logger.info('Step 1: Creating bucket...');
    const createResponse = await storageClient.createBucket({
      name: TEST_BUCKET,
      public: false,
      fileSizeLimit: 1024 * 1024,
      allowedMimeTypes: ['text/plain']
    });
    expect(createResponse.error).toBeNull();
    expect(createResponse.data).toBeTruthy();
    logger.info('✓ Bucket created successfully');

    // 2. Upload file
    logger.info('Step 2: Uploading file...');
    const uploadResponse = await storageClient.uploadFile(
      TEST_BUCKET,
      TEST_FILE_PATH,
      testFileBuffer,
      {
        contentType: 'text/plain',
        upsert: true
      }
    );
    expect(uploadResponse.error).toBeNull();
    expect(uploadResponse.data?.path).toBe(TEST_FILE_PATH);
    logger.info('✓ File uploaded successfully');

    // 3. List files in bucket
    logger.info('Step 3: Listing files in bucket...');
    const listResponse = await storageClient.listFiles(TEST_BUCKET, 'test-folder');
    expect(listResponse.error).toBeNull();
    expect(listResponse.data).toHaveLength(1);
    const firstFile = listResponse.data?.[0];
    expect(firstFile).toBeDefined();
    expect(firstFile?.name).toBe('test-file.txt');
    logger.info('✓ Files listed successfully');

    // 4. Download file
    logger.info('Step 4: Downloading file...');
    const downloadResponse = await storageClient.downloadFile(TEST_BUCKET, TEST_FILE_PATH);
    expect(downloadResponse.error).toBeNull();
    expect(downloadResponse.data).toBeTruthy();
    expect(downloadResponse.data?.toString()).toBe(TEST_FILE_CONTENT);
    logger.info('✓ File downloaded successfully');

    // 5. Delete file
    logger.info('Step 5: Deleting file...');
    const deleteResponse = await storageClient.deleteFiles(TEST_BUCKET, [TEST_FILE_PATH]);
    expect(deleteResponse.error).toBeNull();
    logger.info('✓ File deleted successfully');

    // 6. Verify bucket is empty
    logger.info('Step 6: Verifying bucket is empty...');
    const finalListResponse = await storageClient.listFiles(TEST_BUCKET);
    expect(finalListResponse.error).toBeNull();
    expect(finalListResponse.data).toHaveLength(0);
    logger.info('✓ Bucket is empty');

    logger.info('✓ All storage operations completed successfully');
  });
}); */