import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters')
});

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters')
});

type ValidationResult = {
  success: boolean;
  error?: string;
};

export const validateLoginInput = (data: unknown): ValidationResult => {
  try {
    loginSchema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError && error.errors.length > 0) {
      return {
        success: false,
        error: error.errors[0].message
      };
    }
    return {
      success: false,
      error: 'Invalid input data'
    };
  }
};

export const validateRegisterInput = (data: unknown): ValidationResult => {
  try {
    registerSchema.parse(data);
    return { success: true };
  } catch (error) {
    if (error instanceof z.ZodError && error.errors.length > 0) {
      return {
        success: false,
        error: error.errors[0].message
      };
    }
    return {
      success: false,
      error: 'Invalid input data'
    };
  }
}; 