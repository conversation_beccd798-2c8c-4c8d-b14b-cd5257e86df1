import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useRoute, RouteProp } from '@react-navigation/native';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { FoodStackParamList } from '@navigation/types';
import { FoodItem } from '../../types/food';
import { apiService } from '../../services/api';

type FoodDetailsScreenRouteProp = RouteProp<FoodStackParamList, 'FoodDetails'>;

// Helper function to check if a string is an emoji
const isEmoji = (str: string | undefined): boolean => {
  if (!str) return false;
  return /[\u{1F300}-\u{1F9FF}]/u.test(str);
};

export default function FoodDetailsScreen() {
  const route = useRoute<FoodDetailsScreenRouteProp>();
  const { foodId } = route.params;
  const [foodItem, setFoodItem] = useState<FoodItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState(false);

  useEffect(() => {
    loadFoodItem();
  }, [foodId]);

  const loadFoodItem = async () => {
    try {
      console.log('[FoodDetailsScreen] Loading food item:', foodId);
      const response = await apiService.getFoodItem(foodId);
      console.log('[FoodDetailsScreen] API Response:', {
        hasData: !!response.data,
        hasError: !!response.error,
        foodItem: response.data ? {
          id: response.data.id,
          name: response.data.name,
          hasImageId: !!response.data.imageId,
          imageId: response.data.imageId,
          icon: response.data.icon,
          iconSet: response.data.iconSet
        } : null
      });

      if (response.data) {
        setFoodItem(response.data);

        // Load image if imageId is present
        if (response.data.imageId) {
          loadFoodItemImage(response.data.imageId);
        }
      }
    } catch (error) {
      console.error('[FoodDetailsScreen] Error loading food item:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFoodItemImage = async (imageId: string) => {
    try {
      setImageLoading(true);
      console.log('[FoodDetailsScreen] Loading image:', imageId);
      const imageDataUrl = await apiService.getFoodItemImage(imageId);
      setImageUrl(imageDataUrl);
      console.log('[FoodDetailsScreen] Image loaded successfully');
    } catch (error) {
      console.error('[FoodDetailsScreen] Error loading image:', error);
      setImageError(true);
    } finally {
      setImageLoading(false);
    }
  };

  const handleImageError = (error: any) => {
    console.error('[FoodDetailsScreen] Failed to load image for food item:', {
      id: foodItem?.id,
      imageLength: foodItem?.image?.length,
      imagePrefix: foodItem?.image?.substring(0, 50),
      error: error?.nativeEvent?.error
    });
    setImageError(true);
  };

  const getImageUri = (imageData: string): string => {
    console.log('[FoodDetailsScreen] Processing image URI:', {
      length: imageData.length,
      prefix: imageData.substring(0, 50),
      hasDataPrefix: imageData.startsWith('data:image/')
    });

    // Check if the image data already contains the data URI prefix
    if (imageData.startsWith('data:image/')) {
      return imageData;
    }
    // If not, add the prefix
    return `data:image/jpeg;base64,${imageData}`;
  };

  if (loading) {
    console.log('[FoodDetailsScreen] Loading state');
    return (
      <View style={styles.centerContainer}>
        <Text>טוען פרטי מוצר...</Text>
      </View>
    );
  }

  if (!foodItem) {
    console.log('[FoodDetailsScreen] No food item found');
    return (
      <View style={styles.centerContainer}>
        <Text>מוצר לא נמצא</Text>
      </View>
    );
  }

  console.log('[FoodDetailsScreen] Rendering food item:', {
    id: foodItem.id,
    name: foodItem.name,
    hasImage: !!foodItem.image,
    icon: foodItem.icon,
    iconSet: foodItem.iconSet,
    imageError
  });

  return (
    <View style={styles.container}>
      <Header
        title="🍲 פרטי המוצר"
        showBackButton={true}
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {(imageUrl || foodItem.image) && !imageError && (
          <View style={styles.imageContainer}>
            {imageLoading ? (
              <View style={styles.imageLoadingContainer}>
                <Text>טוען תמונה...</Text>
              </View>
            ) : (
              <Image
                source={{ uri: imageUrl || getImageUri(foodItem.image || '') }}
                style={styles.foodImage}
                resizeMode="cover"
                onError={handleImageError}
              />
            )}
          </View>
        )}

        <View style={styles.foodCard}>
          <View style={styles.foodIcon}>
            {isEmoji(foodItem.icon) ? (
              <Text style={styles.foodIconText}>{foodItem.icon || '🍲'}</Text>
            ) : (
              <Icon
                name={foodItem.icon || 'food'}
                iconSet={foodItem.iconSet || 'MaterialCommunityIcons'}
                size={24}
                color="#fff"
              />
            )}
          </View>

          <View style={styles.cardContent}>
            <Text style={styles.foodTitle}>{foodItem.name}</Text>
            <Text style={styles.foodDescription}>{foodItem.description}</Text>

            <View style={styles.foodMeta}>
              {foodItem.pickupDetails && (
                <Text style={styles.metaText}>📍 {foodItem.pickupDetails}</Text>
              )}
              <Text style={styles.metaText}>👤 {foodItem.sellerName}</Text>
            </View>
          </View>

          <View style={styles.priceTag}>
            <Text style={styles.priceText}>₪{foodItem.price}</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.contactButton}>
          <Text style={styles.contactButtonText}>📞 צור קשר</Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  imageContainer: {
    width: '100%',
    height: 200,
    borderRadius: getBorderRadius('xl'),
    marginBottom: getSpacing('lg'),
    backgroundColor: getColor('neutral', 200),
    overflow: 'hidden',
  },
  imageLoadingContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: getColor('neutral', 100),
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  foodCard: {
    flexDirection: 'row',
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  foodIcon: {
    width: 50,
    height: 50,
    borderRadius: getBorderRadius('lg'),
    backgroundColor: '#f9ca24',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getSpacing('lg'),
  },
  foodIconText: {
    fontSize: 24,
  },
  cardContent: {
    flex: 1,
  },
  foodTitle: {
    ...getTypography('lg', 'bold'),
    color: getColor('primary'),
    marginBottom: getSpacing('xs'),
  },
  foodDescription: {
    ...getTypography('sm'),
    color: getColor('neutral', 600),
    marginBottom: getSpacing('sm'),
  },
  foodMeta: {
    flexDirection: 'row',
    gap: getSpacing('md'),
  },
  metaText: {
    ...getTypography('sm'),
    color: getColor('neutral', 500),
  },
  priceTag: {
    backgroundColor: '#27ae60',
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('xs'),
    marginLeft: getSpacing('md'),
  },
  priceText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  contactButton: {
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('full'),
    padding: getSpacing('lg'),
    alignItems: 'center',
    ...getShadow('sm'),
  },
  contactButtonText: {
    color: getColor('white'),
    ...getTypography('base', 'bold'),
  },
}); 