import React from 'react';
import { StyleSheet, Platform, View } from 'react-native';
import { TextInput, useTheme, Text } from 'react-native-paper';
import { rtlConfig } from '../../theme/rtl';
import { getColor, getSpacing, getBorderRadius, getTypography } from '../../theme';
import { getTextAlign } from '../../utils/rtl';

interface InputProps {
  label?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  style?: any;
  multiline?: boolean;
  numberOfLines?: number;
  placeholder?: string;
  left?: React.ReactNode;
  right?: React.ReactNode;
  maxLength?: number;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  disabled?: boolean;
  containerStyle?: any;
  inputStyle?: any;
  labelStyle?: any;
  errorStyle?: any;
  outlineColor?: string;
  activeOutlineColor?: string;
}

export const Input = ({
  label,
  value,
  onChangeText,
  error,
  secureTextEntry = false,
  keyboardType = 'default',
  style,
  multiline = false,
  numberOfLines = 1,
  placeholder,
  left,
  right,
  maxLength,
  autoCapitalize = 'none',
  disabled = false,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  outlineColor,
  activeOutlineColor,
}: InputProps) => {
  const theme = useTheme();
  const isRTL = getTextAlign() === 'right';

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
        </Text>
      )}
      <View style={[
        styles.inputContainer,
        error && styles.inputContainerError,
        disabled && styles.inputContainerDisabled
      ]}>
        <TextInput
          label={undefined} // We handle the label ourselves for better control
          value={value}
          onChangeText={onChangeText}
          error={!!error}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          style={[
            styles.input,
            { backgroundColor: '#f8f9fa' },
            multiline && styles.multilineInput,
            style,
            isRTL && styles.inputRTL,
            left && (isRTL ? styles.inputWithRightIcon : styles.inputWithLeftIcon),
            right && (isRTL ? styles.inputWithLeftIcon : styles.inputWithRightIcon),
            inputStyle
          ]}
          mode="outlined"
          textAlign={rtlConfig.textAlign}
          textContentType={secureTextEntry ? 'password' : 'none'}
          multiline={multiline}
          numberOfLines={numberOfLines}
          placeholder={placeholder}
          left={left}
          right={right}
          maxLength={maxLength}
          autoCapitalize={autoCapitalize}
          disabled={disabled}
          // Android-specific properties for proper Hebrew input
          autoComplete={Platform.OS === 'android' ? 'off' : undefined}
          autoCorrect={false}
          textAlignVertical={multiline ? 'top' : 'center'}
          placeholderTextColor={getColor('neutral', 400)}
          outlineColor={outlineColor}
          activeOutlineColor={activeOutlineColor}
        />
      </View>
      {error && (
        <Text style={[styles.error, errorStyle]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: getSpacing('md'),
  },
  label: {
    color: getColor('neutral', 700),
    fontWeight: '600',
    marginBottom: getSpacing('xs'),
    fontSize: 14,
    textAlign: 'right',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: getColor('neutral', 200),
    borderRadius: getBorderRadius('md'),
    minHeight: 40,
  },
  inputContainerError: {
    borderColor: getColor('error'),
  },
  inputContainerDisabled: {
    backgroundColor: getColor('neutral', 100),
    opacity: 0.7,
  },
  input: {
    flex: 1,
    ...getTypography('base'),
    color: getColor('neutral', 900),
    paddingHorizontal: getSpacing('md'),
    paddingVertical: getSpacing('xs'),
    textAlign: getTextAlign(),
  },
  inputRTL: {
    textAlign: 'right',
  },
  inputWithLeftIcon: {
    paddingLeft: getSpacing('sm'),
  },
  inputWithRightIcon: {
    paddingRight: getSpacing('sm'),
  },
  error: {
    ...getTypography('sm'),
    color: getColor('error'),
    marginTop: getSpacing('xs'),
  },
  multilineInput: {
    minHeight: 80,
  },
}); 