---
description: 
globs: 
alwaysApply: false
---

# 📆📱 Cross-Platform Date & Time Picker Guidelines  
**Target Tech Stack:** React Native (Expo) – supports Web, Android, iOS

---

## 🎯 Objective

Provide **consistent, native-feeling, and accessible** behavior for **date and time input fields** across **Web, Android, and iOS** using **React Native with Expo**.

---

## 🛠️ Technologies & Assumptions

- Framework: React Native with Expo
- Platform targets: Web, Android, iOS
- Native module: `@react-native-community/datetimepicker`
- Web fallback: Native HTML5 `<input>` or custom modal
- Timezone: Stored in UTC, displayed in local time
- Styling: Cross-platform consistency via theming

---

## 📅 Date Picker — Unified Requirements

| Requirement | Details |
|-------------|---------|
| **Use native picker** | Use `@react-native-community/datetimepicker` for Android/iOS |
| **Web fallback** | Use `<input type="date">` or a styled custom date picker |
| **Default value** | Current date or the next logical selectable date |
| **Validation** | Block past dates when future-only input is required |
| **Format** | Display according to user locale (e.g., `16 ביוני 2025`) |
| **Accessibility** | Provide label and ARIA roles where applicable (Web) |
| **Styling** | Ensure visual alignment across platforms |
| **Timezone** | Display in user’s local time, store in UTC |

---

## ⏰ Time Picker — Unified Requirements

### Mobile (iOS & Android)

| Requirement | Details |
|-------------|---------|
| **Component** | Use `@react-native-community/datetimepicker` with `mode="time"` |
| **Display** | Use `spinner` for iOS, `default` for Android |
| **Format** | Automatically use 12h or 24h based on locale |
| **Default value** | Current time or rounded-up next interval (e.g., 5 or 30 mins) |
| **Validation** | Optional: prevent selection of past times or conflicting slots |
| **Storage** | Always convert and store as UTC (`toISOString()`) |
| **Time zone** | Display in local time |
| **Styling** | Align button and picker size with your app design |
| **Accessibility** | Works natively; no extra steps required |

### Web (React Native for Web)

| Requirement | Details |
|-------------|---------|
| **Avoid typing-only `<input type="time">`** | Native browser implementations are inconsistent, require manual selection to edit |
| **Use controlled input** | Manage with `value` and `onChange`, use `step="300"` (5-minute increments) |
| **Custom modal fallback (preferred)** | Use dropdowns, sliders, or scroll pickers for consistent UX |
| **Keyboard support** | Ensure all components can be operated with keyboard |
| **Accessibility** | Add `aria-label`, focus management for modals |
| **Styling** | Match design language of mobile pickers |

#### Web Example (Fallback)

```tsx
{Platform.OS === 'web' ? (
  <input
    type="time"
    step="300"
    value={formattedTime}
    onChange={(e) => setTime(e.target.value)}
    style={{ fontSize: 16, padding: 8 }}
  />
) : (
  <DateTimePicker
    mode="time"
    value={time}
    is24Hour={is24Hour}
    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
    onChange={handleChange}
  />
)}
```

---

## 🌍 Locale & Time Format Handling

| Aspect | Recommendation |
|--------|----------------|
| **Detect 24h/12h format** | Use `Intl.DateTimeFormat().resolvedOptions().hour12` |
| **Respect locale** | Format using `Intl.DateTimeFormat` or `date-fns` |
| **Store in UTC** | Use `Date.toISOString()` to persist time in UTC |
| **Convert on display** | Adjust using `new Date(time).toLocaleTimeString()` |

---

## ✅ UX & Functional Checklist

| UX Element | Requirement |
|------------|-------------|
| Input alignment | Right-aligned for Hebrew, consistent across platforms |
| Default values | Logical (e.g., current time/date, next available) |
| Validation | Time not in the past, consistent rules for all platforms |
| Keyboard support (Web) | No mouse-only controls |
| Mobile-first design | Optimize for touch interactions |
| Field labels | Clear, consistent (`בחר שעה`, `בחר תאריך`) |
| Accessibility | ARIA roles for web inputs or modals |

---

## 🧪 Testing Strategy

| Platform | Test Cases |
|----------|------------|
| Web | Typing behavior, keyboard navigation, modal fallback, RTL support |
| Android | Native time/date picker behavior, 12/24h format |
| iOS | Spinner layout, consistent theme, accessibility voice-over support |

---

## 📦 Summary of Component Usage

| Platform | Date | Time |
|----------|------|------|
| Android | `@react-native-community/datetimepicker` | `display="default"` |
| iOS | `@react-native-community/datetimepicker` | `display="spinner"` |
| Web | `<input type="date">` or styled picker | Custom modal **or** controlled `<input type="time">` with `step="300"` |

---

Let us know if you want a reusable `TimePicker` component with full Web/mobile fallback built-in.

