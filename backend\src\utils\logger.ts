import winston from 'winston';
import 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';
import { envRegistry } from './env';

// Load environment variables
dotenv.config();

// Create logs directory if it doesn't exist
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// Define log level type
type LogLevel = 'fatal' | 'error' | 'warn' | 'info' | 'debug';

// Custom log levels
const levels: Record<LogLevel, number> = {
  fatal: 0,
  error: 1,
  warn: 2,
  info: 3,
  debug: 4,
};

// Custom colors for console output
const colors = {
  fatal: 'red',
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
};

// Add colors to winston
winston.addColors(colors);

// Store module-specific log levels
const moduleLevels = new Map<string, LogLevel>();

// Function to validate log level
const isValidLogLevel = (level: string): level is LogLevel => {
  return level in levels;
};

// Function to convert module name to environment variable format
const moduleNameToEnvKey = (moduleName: string): string => {
  // Convert camelCase to SNAKE_CASE, but don't add underscore at start
  const snakeCase = moduleName
    .replace(/([A-Z])/g, '_$1')
    .toUpperCase()
    .replace(/^_/, ''); // Remove leading underscore
  
  // Handle special cases
  const result = snakeCase
    .replace(/CONTROLLER$/, '_CTRL')
    .replace(/SERVICE$/, '_SVC')
    .replace(/LOGGER$/, '_LOG');

  // Add debug logging only if LOG_DEBUG is set to true
  if (process.env['LOG_DEBUG'] === 'true') {
    console.log('Module Name Conversion:', {
      original: moduleName,
      snakeCase,
      result
    });
  }

  return result;
};

// Function to get log level from environment variable
const getLogLevelFromEnv = (moduleName: string): LogLevel => {
  const envKey = `LOG_LEVEL_${moduleNameToEnvKey(moduleName)}`;
  const moduleLevel = process.env[envKey]?.toLowerCase();
  const globalLevel = process.env['LOG_LEVEL']?.toLowerCase();
  
  // Determine the actual final level
  let finalLevel: LogLevel;
  if (moduleLevel && isValidLogLevel(moduleLevel)) {
    finalLevel = moduleLevel;
  } else if (globalLevel && isValidLogLevel(globalLevel)) {
    finalLevel = globalLevel;
  } else {
    finalLevel = 'info';
  }
  
  // Add debug logging to see what's happening
  //console.log('Logger Environment Check:', {
   // moduleName,
    //envKey,
    //moduleLevel: moduleLevel || 'not set',
    //globalLevel: globalLevel || 'not set',
    //finalLevel
  //});
  
  return finalLevel;
};

// Function to set log level for a specific module
const setModuleLevel = (moduleName: string, level: LogLevel) => {
  if (!isValidLogLevel(level)) {
    throw new Error(`Invalid log level: ${level}. Must be one of: ${Object.keys(levels).join(', ')}`);
  }
  moduleLevels.set(moduleName, level);
};

// Function to get log level for a specific module
const getModuleLevel = (moduleName: string): LogLevel => {
  // First check if level was set programmatically
  const programmaticLevel = moduleLevels.get(moduleName);
  if (programmaticLevel) {
    return programmaticLevel;
  }
  
  // Then check environment variables
  return getLogLevelFromEnv(moduleName);
};

// Create the base logger
const baseLogger = winston.createLogger({
  levels,
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss',
    }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  transports: [
    // Console transport with colors
    new winston.transports.Console({
      level: 'debug', // Set to debug to allow all levels
      format: winston.format.combine(
        winston.format.colorize({ all: true }),
        winston.format.printf(({ timestamp, level, message, module, ...meta }) => {
          const metaString = Object.keys(meta).length 
            ? '\n' + JSON.stringify(meta, null, 2)
            : '';
          return `${timestamp} [${module || 'GLOBAL'}] ${level}: ${message}${metaString}`;
        })
      ),
    }),
    // File transport without colors
    new winston.transports.DailyRotateFile({
      filename: path.join(logDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '10m',
      maxFiles: '30d',
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss',
        }),
        winston.format.json()
      ),
    }),
    new winston.transports.DailyRotateFile({
      filename: path.join(logDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '10m',
      maxFiles: '30d',
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss',
        }),
        winston.format.json()
      ),
    }),
  ],
});

// Register environment variables after logger is created
envRegistry.registerMultiple([
  {
    name: 'LOG_LEVEL',
    description: 'Global logging level (fatal, error, warn, info, debug)',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_DEBUG',
    description: 'Enable debug logging for logger configuration',
    defaultValue: 'false',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_LEVEL_JOB',
    description: 'Job module logging level',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_LEVEL_AUTH',
    description: 'Auth module logging level',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_LEVEL_MAINTENANCE',
    description: 'Maintenance module logging level',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_LEVEL_COMMON',
    description: 'Common module logging level',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_LEVEL_SUPABASE',
    description: 'Supabase module logging level',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  },
  {
    name: 'LOG_LEVEL_DATABASE',
    description: 'Database module logging level',
    defaultValue: 'info',
    required: false,
    module: 'Logging'
  }
]);

// Create a function to get a module-specific logger
const getModuleLogger = (moduleName: string) => {
  const moduleLevel = getModuleLevel(moduleName);
  
  return {
    setLevel: (level: LogLevel) => setModuleLevel(moduleName, level),
    getLevel: () => getModuleLevel(moduleName),
    fatal: (message: string, meta?: any) => {
      if (levels[moduleLevel] >= levels.fatal) {
        baseLogger.log('fatal', message, { module: moduleName, ...meta });
      }
    },
    error: (message: string, meta?: any) => {
      if (levels[moduleLevel] >= levels.error) {
        baseLogger.log('error', message, { module: moduleName, ...meta });
      }
    },
    warn: (message: string, meta?: any) => {
      if (levels[moduleLevel] >= levels.warn) {
        baseLogger.log('warn', message, { module: moduleName, ...meta });
      }
    },
    info: (message: string, meta?: any) => {
      if (levels[moduleLevel] >= levels.info) {
        baseLogger.log('info', message, { module: moduleName, ...meta });
      }
    },
    debug: (message: string, meta?: any) => {
      if (levels[moduleLevel] >= levels.debug) {
        baseLogger.log('debug', message, { module: moduleName, ...meta });
      }
    },
  };
};

// Create a stream for real-time log monitoring
const logStream = {
  write: (message: string) => {
    baseLogger.info(message.trim());
  },
};

// Subscribe to logs for real-time streaming
const subscribers = new Set<(log: string) => void>();

const subscribeToLogs = (callback: (log: string) => void) => {
  subscribers.add(callback);
  return () => subscribers.delete(callback);
};

// Override the logger's write method to notify subscribers
const originalWrite = baseLogger.write.bind(baseLogger);
baseLogger.write = (info: any) => {
  const result = originalWrite(info);
  const logMessage = JSON.stringify(info);
  subscribers.forEach((callback) => callback(logMessage));
  return result;
};

export { baseLogger as logger, getModuleLogger, logStream, subscribeToLogs }; 