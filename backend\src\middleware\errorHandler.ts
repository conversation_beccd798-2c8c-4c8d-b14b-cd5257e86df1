import { Request, Response } from 'express';
import { getModuleLogger } from '../utils/logger';
import { createErrorResponse } from '../types/errors';

const logger = getModuleLogger('ErrorHandler');

// Add debug logging to verify log level
logger.debug('ErrorHandler module initialized with level:', { level: logger.getLevel() });

export const errorHandler = (err: Error, req: Request, res: Response) => {
  // Log the full error details
  logger.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
    body: req.body,
    params: req.params,
    query: req.query
  });

  // Don't leak error details in production
  const isDevelopment = process.env['NODE_ENV'] === 'development';

  // Determine status code based on error type
  let statusCode = 500;
  let errorMessage = 'Internal Server Error';

  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    errorMessage = 'Validation Error';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    errorMessage = 'Unauthorized';
  } else if (err.name === 'ForbiddenError') {
    statusCode = 403;
    errorMessage = 'Forbidden';
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    errorMessage = 'Not Found';
  }

  res.status(statusCode).json(createErrorResponse(
    errorMessage,
    isDevelopment ? err.message : 'Something went wrong!',
    isDevelopment ? {
      stack: err.stack,
      name: err.name
    } : undefined
  ));
};