// API Service for Custom Backend Communication
import { API_BASE_URL } from '../lib/supabase';
import { FoodItem, CreateFoodItemData } from '../types/food';
import { Event, Job } from '../types';

interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  status?: number;
}

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  private token: string | null = null;

  setToken(token: string | null) {
    this.token = token;
    console.log('🎫 API service token updated:', !!token);
  }

  private async getAuthToken(): Promise<string | null> {
    return this.token;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {};

    // Add auth token if available
    const token = await this.getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // If the body is FormData, don't set Content-Type header
    // Let the browser set it with the boundary
    if (!(options.body instanceof FormData)) {
      headers['Content-Type'] = 'application/json';
    }

    // Log request details
    console.log('API Request:', {
      url,
      method: options.method,
      headers,
      hasFormData: options.body instanceof FormData,
      bodyType: options.body instanceof FormData ? 'FormData' : typeof options.body
    });

    // If it's FormData, log its contents
    if (options.body instanceof FormData) {
      console.log('FormData contents:');
      for (const [key, value] of (options.body as any).entries()) {
        console.log(`${key}:`, value);
      }
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error occurred' }));
        console.error('API request failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });

        return {
          data: errorData,
          error: errorData.message || 'Request failed',
          status: response.status
        };
      }

      const data = await response.json();
      return { data, error: null, status: response.status };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 500
      };
    }
  }

  // Event API methods
  async getEvents(): Promise<ApiResponse<Event[]>> {
    return this.request('/events');
  }

  async getEvent(id: string): Promise<ApiResponse<Event>> {
    return this.request(`/events/${id}`);
  }

  async createEvent(eventData: Partial<Event>): Promise<ApiResponse<Event>> {
    return this.request('/events', {
      method: 'POST',
      body: JSON.stringify(eventData),
    });
  }

  async updateEvent(id: string, eventData: Partial<Event>): Promise<ApiResponse<Event>> {
    return this.request(`/events/${id}`, {
      method: 'PUT',
      body: JSON.stringify(eventData),
    });
  }

  async deleteEvent(id: string): Promise<ApiResponse<void>> {
    return this.request(`/events/${id}`, {
      method: 'DELETE',
    });
  }

  // Food API methods
  async getFoodItems(): Promise<ApiResponse<FoodItem[]>> {
    return this.request('/food');
  }

  async getFoodItem(id: string): Promise<ApiResponse<FoodItem>> {
    console.log('[API] Fetching food item:', id);
    return this.request(`/food/${id}`);
  }

  async getFoodItemImage(imageId: string): Promise<string> {
    console.log('[API] Fetching food item image:', imageId);
    const response = await fetch(`${this.baseUrl}/food/images/${imageId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${await this.getAuthToken()}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }

    // Convert the image response to a blob and then to a data URL
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  async createFoodItem(foodData: CreateFoodItemData | FormData): Promise<ApiResponse<FoodItem>> {
    const headers: Record<string, string> = {};
    
    // If foodData is FormData, don't set Content-Type header
    // Let the browser set it with the boundary
    if (!(foodData instanceof FormData)) {
      headers['Content-Type'] = 'application/json';
    }

    return this.request('/food', {
      method: 'POST',
      headers,
      body: foodData instanceof FormData ? foodData : JSON.stringify(foodData),
    });
  }

  async updateFoodItem(id: string, foodData: Partial<CreateFoodItemData>): Promise<ApiResponse<FoodItem>> {
    return this.request(`/food/${id}`, {
      method: 'PUT',
      body: JSON.stringify(foodData),
    });
  }

  async deleteFoodItem(id: string): Promise<ApiResponse<void>> {
    return this.request(`/food/${id}`, {
      method: 'DELETE',
    });
  }

  // Job API methods
  async getJobs(): Promise<ApiResponse<Job[]>> {
    return this.request('/jobs');
  }

  async createJob(jobData: Partial<Job>): Promise<ApiResponse<Job>> {
    return this.request('/jobs', {
      method: 'POST',
      body: JSON.stringify(jobData),
    });
  }

  // User API methods
  async getUserProfile(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/users/${userId}`);
  }

  async createUserProfile(userData: any): Promise<ApiResponse<any>> {
    return this.request('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUserProfile(userId: string, userData: any): Promise<ApiResponse<any>> {
    return this.request(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async getAllUsers(): Promise<ApiResponse<any[]>> {
    return this.request('/users');
  }

  async getPeople(): Promise<ApiResponse<any[]>> {
    return this.request('/users/people');
  }

  // Auth API methods
  async register(userData: any): Promise<ApiResponse<any>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async login(credentials: any): Promise<ApiResponse<any>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  // Survey API methods
  async getSurveys(): Promise<ApiResponse<any[]>> {
    return this.request('/surveys');
  }

  async getSurvey(surveyId: string): Promise<ApiResponse<any>> {
    return this.request(`/surveys/${surveyId}`);
  }

  async createSurvey(surveyData: any): Promise<ApiResponse<any>> {
    const headers: Record<string, string> = {};
    
    // If surveyData is FormData, don't set Content-Type header
    // Let the browser set it with the boundary
    if (!(surveyData instanceof FormData)) {
      headers['Content-Type'] = 'application/json';
    }

    return this.request('/surveys', {
      method: 'POST',
      headers,
      body: surveyData instanceof FormData ? surveyData : JSON.stringify(surveyData),
    });
  }

  async updateSurvey(surveyId: string, surveyData: any): Promise<ApiResponse<any>> {
    return this.request(`/surveys/${surveyId}`, {
      method: 'PUT',
      body: JSON.stringify(surveyData),
    });
  }

  async submitSurveyResponse(surveyId: string, responseData: any): Promise<ApiResponse<any>> {
    return this.request(`/surveys/${surveyId}/responses`, {
      method: 'POST',
      body: JSON.stringify(responseData),
    });
  }

  async deleteSurvey(surveyId: string): Promise<ApiResponse<any>> {
    return this.request(`/surveys/${surveyId}`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request('/health');
  }

  // Debug API methods (admin only)
  async getDebugTables(): Promise<ApiResponse<{ tables: string[] }>> {
    return this.request('/debug/tables');
  }

  async getDebugTableEntries(tableName: string, limit: number = 100): Promise<ApiResponse<{ entries: any[], total: number }>> {
    return this.request(`/debug/tables/${tableName}/entries?limit=${limit}`);
  }

  // Chat API methods
  async getChatGroups(): Promise<ApiResponse<any[]>> {
    return this.request('/chat/groups');
  }

  async getChatGroup(groupId: string): Promise<ApiResponse<any>> {
    return this.request(`/chat/groups/${groupId}`);
  }

  async getChatMessages(groupId: string): Promise<ApiResponse<any[]>> {
    return this.request(`/chat/groups/${groupId}/messages`);
  }

  async sendMessage(_groupId: string, message: any): Promise<ApiResponse<any>> {
    // Note: groupId is kept for API consistency but the message object already contains group_id
    // The message should already be formatted with snake_case fields by the chat service
    console.log('🌐 API: Sending message to /chat/messages');
    console.log('📋 API: Message payload:', message);

    return this.request('/chat/messages', {
      method: 'POST',
      body: JSON.stringify(message),
    });
  }

  async createChatGroup(groupData: any): Promise<ApiResponse<any>> {
    return this.request('/chat/groups', {
      method: 'POST',
      body: JSON.stringify(groupData),
    });
  }
}

export const apiService = new ApiService();
export default apiService;
