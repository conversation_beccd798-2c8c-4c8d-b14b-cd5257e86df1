# Expo
.expo/
dist/
web-build/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# macOS
.DS_Store

# Windows
Thumbs.db

# VS Code
.vscode/

# Temporary files
*.tmp
*.temp

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Bundle artifact
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo Web
web-build/

# Expo Go
.expo-shared/

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Testing
coverage/
.nyc_output/

# IDE
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
volumes/db/base/
backend/src/generated/prisma/
backend/volumes/db/
backend/volumes/db/base/
backend/volumes/db/pg_wal/
backend/volumes/db/base/5/
backend/volumes/db/pg_wal/
backend/volumes/elasticsearch/_state/
backend/volumes/elasticsearch/indices/
backend/volumes/elasticsearch/snapshot_cache/
backend/volumes/elasticsearch/node.lock
backend/volumes/elasticsearch/nodes
backend/volumes/minio/
backend/uploads/
