import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Image,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { SearchInput } from '../../components/SearchInput';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { HEADER_ICONS } from '@constants';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { FoodStackParamList } from '@navigation/types';
import { FoodItem } from '../../types/food';
import { apiService } from '../../services/api';

type FoodListScreenNavigationProp = StackNavigationProp<FoodStackParamList, 'FoodList'>;

function isEmoji(str: string | null | undefined) {
  // Simple check: if it's a single character and matches emoji unicode range
  return !!str && str.length <= 2 && /[\u231A-\uD83E\uDDFF]/.test(str);
}

export default function FoodListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<FoodListScreenNavigationProp>();
  const [foodItems, setFoodItems] = useState<FoodItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const loadFoodItems = async () => {
    try {
      console.log('[FoodListScreen] Loading food items...');
      const response = await apiService.getFoodItems();
      console.log('[FoodListScreen] API Response:', {
        hasData: !!response.data,
        hasError: !!response.error,
        dataLength: response.data?.length,
        error: response.error
      });
      if (response.data) {
        setFoodItems(response.data);
      }
    } catch (error) {
      console.error('[FoodListScreen] Error loading food items:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadFoodItems();
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadFoodItems();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    loadFoodItems();
  };

  const handleAddItem = () => {
    navigation.navigate('CreateFood');
  };

  const handleItemPress = (itemId: string) => {
    navigation.navigate('FoodDetails', { foodId: itemId });
  };

  const filteredItems = foodItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderFoodItem = ({ item }: { item: FoodItem }) => (
    <TouchableOpacity
      style={styles.foodCard}
      onPress={() => handleItemPress(item.id)}
    >
      {/* Note: For list view, we'll show images on demand or use thumbnails in the future */}
      <View style={styles.foodIconContainer}>
        {isEmoji(item.icon) ? (
          <Text style={styles.foodIcon}>{item.icon || '🍲'}</Text>
        ) : (
          <Icon
            name={item.icon || 'food'}
            iconSet={item.iconSet || 'MaterialCommunityIcons'}
            size={24}
            color="#fff"
          />
        )}
      </View>

      <View style={styles.cardContent}>
        <Text style={styles.foodTitle}>{item.name}</Text>
        <Text style={styles.foodDescription}>{item.description}</Text>

        <View style={styles.metaRow}>
          <Text style={styles.metaIcon}>👤</Text>
          <Text style={styles.metaText}>{item.sellerName}</Text>
          <Text style={styles.metaIcon}>📍</Text>
          <Text style={styles.metaText}>{item.pickupDetails}</Text>
          {item.imageId && (
            <>
              <Text style={styles.metaIcon}>📷</Text>
              <Text style={styles.metaText}>יש תמונה</Text>
            </>
          )}
        </View>
      </View>

      <View style={styles.priceTag}>
        <Text style={styles.priceText}>₪{item.price}</Text>
      </View>
    </TouchableOpacity>
  );

  const addButton = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleAddItem}
    >
      <Text style={styles.addButtonText}>+ הוסף</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען מוצרי מזון...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title="🍲 מוצרי אוכל"
        showBackButton={true}
        rightComponent={addButton}
      />

      <View style={styles.content}>
        <SearchInput
          placeholder="חיפוש מוצרים..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        <FlatList
          data={filteredItems}
          renderItem={renderFoodItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>

      <TouchableOpacity style={styles.fab} onPress={handleAddItem}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: getSpacing('lg'),
  },
  listContainer: {
    paddingBottom: 100,
  },
  foodCard: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('xl'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('lg'),
    flexDirection: 'row',
    alignItems: 'center',
    ...getShadow('sm'),
  },
  foodIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 12,
    marginRight: getSpacing('md'),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9ca24',
  },
  foodIcon: {
    fontSize: 24,
  },
  cardContent: {
    flex: 1,
  },
  foodTitle: {
    ...getTypography('lg', 'bold'),
    color: getColor('primary'),
    marginBottom: getSpacing('xs'),
  },
  foodDescription: {
    ...getTypography('sm'),
    color: getColor('neutral', 600),
    marginBottom: getSpacing('sm'),
  },
  metaRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    gap: getSpacing('xs'),
    marginTop: getSpacing('sm'),
    writingDirection: 'rtl',
  },
  metaText: {
    ...getTypography('xs'),
    color: getColor('neutral', 400),
    textAlign: 'right',
    marginLeft: getSpacing('xs'),
    writingDirection: 'rtl',
  },
  metaIcon: {
    fontSize: 14,
    marginLeft: getSpacing('xs'),
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  priceTag: {
    backgroundColor: '#27ae60',
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('xs'),
    marginLeft: getSpacing('md'),
  },
  priceText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  fab: {
    position: 'absolute',
    bottom: getSpacing('lg'),
    right: getSpacing('lg'),
    backgroundColor: getColor('primary'),
    borderRadius: getBorderRadius('full'),
    padding: getSpacing('lg'),
    ...getShadow('md'),
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('sm'),
    borderRadius: getBorderRadius('full'),
  },
  addButtonText: {
    color: getColor('white'),
    ...getTypography('sm', 'bold'),
  },
  foodImageContainer: {
    width: '100%',
    height: 150,
    marginBottom: getSpacing('md'),
    borderRadius: getBorderRadius('lg'),
    overflow: 'hidden',
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  fabText: {
    color: getColor('white'),
    ...getTypography('lg', 'bold'),
  },
}); 