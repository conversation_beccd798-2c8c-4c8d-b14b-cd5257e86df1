<!DOCTYPE html>

<html dir="rtl"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Village Community App - Complete Mockup Set</title>
<style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .export-note {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 350px;
            margin: 0 auto;
            position: relative;
        }
        
        .screen-label {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .header-action {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        /* Common Card Styles */
        .feature-card, .event-card, .job-card, .chat-card, .market-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .feature-card:hover, .event-card:hover, .job-card:hover, .chat-card:hover, .market-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .feature-icon, .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            flex-shrink: 0;
        }
        
        /* Icon Colors */
        .events-icon { background: #ff6b6b; }
        .jobs-icon { background: #4ecdc4; }
        .chat-icon { background: #45b7d1; }
        .market-icon { background: #f9ca24; }
        .news-icon { background: #6c5ce7; }
        .profile-icon { background: #a55eea; }
        
        .feature-text, .card-text {
            flex: 1;
        }
        
        .feature-text h3, .card-text h3 {
            font-size: 16px;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .feature-text p, .card-text p {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }
        
        .card-meta {
            font-size: 11px;
            color: #bdc3c7;
            margin-left: 10px;
            text-align: right;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            padding: 10px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .nav-item:hover {
            transform: scale(1.1);
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Search Bar */
        .search-bar {
            background: #f8f9fa;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            width: 100%;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        /* Chat specific */
        .chat-list {
            height: calc(100% - 60px);
        }
        
        .chat-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .chat-content {
            flex: 1;
        }
        
        .chat-name {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
            color: #2c3e50;
        }
        
        .chat-preview {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .chat-time {
            font-size: 11px;
            color: #bdc3c7;
        }
        
        .unread-badge {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }
        
        /* Event specific */
        .event-date {
            background: #667eea;
            color: white;
            border-radius: 8px;
            padding: 8px;
            text-align: center;
            margin-right: 15px;
            min-width: 50px;
        }
        
        .event-day {
            font-size: 18px;
            font-weight: bold;
            line-height: 1;
        }
        
        .event-month {
            font-size: 10px;
            text-transform: uppercase;
        }
        
        /* Market specific */
        .price-tag {
            background: #27ae60;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: auto;
        }
        
        /* Add/Create buttons */
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
    </style>
</head><head></head><body><div class="container"><div class="phone-mockup">
<div class="screen-label">SURVEY HOME</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="app-header"><button class="header-action">חזור</button><div class="header-title">📝 סקרים</div><button class="header-action">חדש</button></div>
<div class="content" style="overflow-y:auto;; text-align: right">
<div class="market-card">
<div class="card-icon">🏗️</div>
<div class="card-text">
<h3 style=" text-align: right">מרכז קהילתי חדש</h3>
<p style=" text-align: right">עזרו לנו לתכנן את המרכז הקהילתי הבא. ענו על מספר שאלות קצרות והשפיעו!</p>
</div>
<div class="price-tag">סקר פתוח</div>
</div>
<div class="market-card">
<div class="card-icon">🎉</div>
<div class="card-text">
<h3 style=" text-align: right">אירועי קיץ 2025</h3>
<p style=" text-align: right">ספרו לנו אילו פעילויות אתם הכי רוצים לראות הקיץ.</p>
</div>
<div class="price-tag">סקר סגור</div>
</div>
</div>
<div class="bottom-nav">
<div class="nav-item">
<div>🏠</div>
<div class="nav-label">Home</div>
</div>
<div class="nav-item active">
<div>📝</div>
<div class="nav-label">Surveys</div>
</div>
<div class="nav-item">
<div>👥</div>
<div class="nav-label">People</div>
</div>
<div class="nav-item">
<div>⚙️</div>
<div class="nav-label">More</div>
</div>
</div>
</div>
</div><div class="phone-mockup">
<div class="screen-label">SURVEY DETAILS</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="app-header"><button class="header-action">חזור</button><div class="header-title">🏗️ מרכז קהילתי</div><button class="header-action">חזור</button></div>
<div class="content" style="overflow-y:auto; padding: 20px;; text-align: right">
<div style="text-align: center; margin-bottom: 15px;">
<img alt="Survey" src="https://via.placeholder.com/300x150?text=Survey+Image" style="width:100%; border-radius: 12px;"/>
</div>
<h3 style="color:#2c3e50; font-size: 18px; margin-bottom: 5px;; text-align: right">תכנון מרכז קהילתי חדש</h3>
<p style="color: #7f8c8d; font-size: 14px; margin-bottom: 15px;; text-align: right">
        אנו בונים מרכז חדש לקהילה – שתפו אותנו מה חשוב לכם שיהיה בו: חוגים, אזורי ישיבה, משחקים לילדים ועוד.
      </p>
<div style="font-size: 13px; color: #95a5a6; margin-bottom: 10px;">
        🗓️ סיום הסקר: <strong>30 ביוני 2025</strong><br/>
        👥 משתתפים עד כה: <strong>142</strong>
</div>
<button class="modal-button primary" style="width: 100%;">השתתפו עכשיו</button>
</div>
<div class="bottom-nav">
<div class="nav-item">
<div>🏠</div>
<div class="nav-label">Home</div>
</div>
<div class="nav-item active">
<div>📝</div>
<div class="nav-label">Surveys</div>
</div>
<div class="nav-item">
<div>👥</div>
<div class="nav-label">People</div>
</div>
<div class="nav-item">
<div>⚙️</div>
<div class="nav-label">More</div>
</div>
</div>
</div>
</div><div class="phone-mockup">
<div class="screen-label">CREATE SURVEY</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="app-header"><button class="header-action">חזור</button><div class="header-title">📝 יצירת סקר</div><button class="header-action">שמור</button></div>
<div class="content" style="overflow-y:auto; padding: 15px;; text-align: right">
<label style=" text-align: right">כותרת הסקר</label>
<input placeholder="לדוגמה: מה חשוב לכם במרכז הקהילתי?" style="width:100%; margin-bottom:10px;; text-align: right" type="text"/>
<label style=" text-align: right">תיאור</label>
<textarea style="width:100%; height:60px; margin-bottom:10px;; text-align: right">אנא שתפו מה חשוב לכם שיהיה בפרויקט החדש</textarea>
<label style=" text-align: right">שאלות</label>
<div style="margin-bottom:15px;">
<div style="background:#f9f9f9; padding:10px; border-radius:8px; margin-bottom:10px;">
<input placeholder="שאלה 1: מה הכי חשוב לכם?" style="width:100%; margin-bottom:8px;; text-align: right" type="text"/>
<div>
<input placeholder="תשובה אפשרית 1" style="width:calc(100% - 40px); margin-bottom:4px; display:inline-block;; text-align: right" type="text"/>
<button style="width:30px; display:inline-block; font-size:18px; background:#ecf0f1; border:none; border-radius:5px;">➕</button>
</div>
</div>
<button class="modal-button" style="width:100%; background:#ecf0f1; color:#2c3e50;">➕ הוסף שאלה נוספת</button>
</div>
<label style=" text-align: right">תאריך סיום</label>
<input style="width:100%; margin-bottom:10px;; text-align: right" type="date"/>
<label style=" text-align: right">תמונה (אופציונלי)</label>
<input accept="image/*" style="width:100%; margin-bottom:20px;; text-align: right" type="file"/>
<button class="modal-button primary" style="width:100%;">צור סקר</button>
</div>
</div>
</div><div class="phone-mockup">
<div class="screen-label">ANSWER SURVEY</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="app-header"><button class="header-action">חזור</button><div class="header-title">📋 ענו על הסקר</div><button class="header-action">חזור</button></div>
<div class="content" style="overflow-y:auto; padding: 15px;; text-align: right">
<h3 style="margin-bottom: 10px;; text-align: right">מהו הדבר החשוב ביותר במרכז קהילתי?</h3>
<label style=" text-align: right"><input name="q1" style=" text-align: right" type="radio"/> אזור ישיבה</label><br/>
<label style=" text-align: right"><input name="q1" style=" text-align: right" type="radio"/> חוגים</label><br/>
<label style=" text-align: right"><input name="q1" style=" text-align: right" type="radio"/> משחקים לילדים</label><br/>
<label style=" text-align: right"><input name="q1" style=" text-align: right" type="radio"/> מענה לאוכלוסייה מבוגרת</label><br/><br/>
<h3 style="margin-bottom: 10px;; text-align: right">באילו שעות הייתם רוצים שהמרכז יהיה פתוח?</h3>
<label style=" text-align: right"><input name="q2" style=" text-align: right" type="checkbox"/> בוקר</label><br/>
<label style=" text-align: right"><input name="q2" style=" text-align: right" type="checkbox"/> צהריים</label><br/>
<label style=" text-align: right"><input name="q2" style=" text-align: right" type="checkbox"/> ערב</label><br/><br/>
<label style=" text-align: right">הערות נוספות</label>
<textarea style="width:100%; height:60px; margin-bottom:15px;; text-align: right"></textarea>
<button class="modal-button primary" style="width:100%;">שלח תשובות</button>
</div>
</div>
</div>
<div class="phone-mockup">
<div class="screen-label">SURVEY RESULTS</div>
<div class="screen">
<div class="status-bar">
<span>9:41</span>
<span>🔋 100%</span>
</div>
<div class="app-header"><button class="header-action">חזור</button><div class="header-title">📊 תוצאות הסקר</div><button class="header-action">סגור</button></div>
<div class="content" style="overflow-y:auto; padding: 15px;; text-align: right">
<h3 style="margin-bottom: 10px;; text-align: right">מהו הדבר החשוב ביותר במרכז קהילתי?</h3>
<div style="margin-bottom: 15px;">
<div>🪑 אזור ישיבה - <strong>45%</strong></div>
<div>🎨 חוגים - <strong>30%</strong></div>
<div>🧸 משחקים לילדים - <strong>15%</strong></div>
<div>👴 אוכלוסייה מבוגרת - <strong>10%</strong></div>
</div>
<h3 style="margin-bottom: 10px;; text-align: right">באילו שעות הייתם רוצים שהמרכז יהיה פתוח?</h3>
<div style="margin-bottom: 15px;">
<div>🌅 בוקר - <strong>60%</strong></div>
<div>🌞 צהריים - <strong>50%</strong></div>
<div>🌙 ערב - <strong>80%</strong></div>
</div>
<h3 style="margin-bottom: 10px;; text-align: right">הערות כלליות:</h3>
<ul style="font-size: 13px; color: #2c3e50; margin-left: 15px;; text-align: right">
<li style=" text-align: right">הייתי רוצה שיהיה מקום להורים עם ילדים קטנים.</li>
<li style=" text-align: right">נא לדאוג לחנייה נגישה.</li>
<li style=" text-align: right">חשוב שתהיה תאורה טובה בערב.</li>
</ul>
</div>
</div>
</div>
</div></body></html>