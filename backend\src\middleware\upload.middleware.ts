import multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

// Get max file size from environment variable (default to 5MB if not set)
const MAX_FILE_SIZE = parseInt(process.env['MAX_FILE_SIZE'] || '5242880', 10);

// Configure storage to use memory
const storage = multer.memoryStorage();

// File filter
const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

// Create multer instance
export const upload = multer({
  storage: storage,
  limits: {
    fileSize: MAX_FILE_SIZE, // Use the value from environment variable
  },
  fileFilter: fileFilter
});

// Error handling middleware for multer
export const handleMulterError = (error: any, _req: Request, res: Response, next: NextFunction) => {
  if (error instanceof Error) {
    logger.error('Multer error:', error);
    
    if (error.message === 'Only image files are allowed') {
      return res.status(400).json({
        message: error.message,
        code: 'INVALID_FILE_TYPE',
        success: false
      });
    }

    if (error.message.includes('File too large')) {
      return res.status(400).json({
        message: 'File size exceeds the limit of 10MB',
        code: 'FILE_TOO_LARGE',
        success: false
      });
    }
  }

  return next(error);
}; 