---
description: 
globs: 
alwaysApply: false
---
**Unified Top Bar (App Header) Design Rules - RTL Layout for React Native (Expo Web + Mobile)**

This guide defines a consistent top bar (header) structure for all mobile and web screens in a Hebrew (RTL) context, tailored for React Native using Expo.

---

### 🔢 Layout Structure

```plaintext
+------------------------------------------------+
| 🔙 Back     |  📑 Icon + Title    | Action (New, Save) |
+------------------------------------------------+
```

- Use `flexDirection: 'row-reverse'` in React Native to handle RTL layout.
- **Back Button**: Always rendered first in JSX but visually appears on the left due to `row-reverse`.
- **Title**: Centered via `flex: 1`, textAlign: 'center'.
- **Action Button**: Rendered last and aligned to the right (visually on the left in RTL).
- **Optional Action**: If no action needed, render a placeholder `View` to maintain layout spacing.

---

### ✅ React Native JSX Example

```jsx
<View style={styles.header}>
  <TouchableOpacity onPress={handleBack}>
    <Text style={styles.headerAction}>🔙 חזור</Text>
  </TouchableOpacity>

  <Text style={styles.headerTitle}>📑 סקרים</Text>

  <TouchableOpacity onPress={handleNew}>
    <Text style={styles.headerAction}>➕ חדש</Text>
  </TouchableOpacity>
</View>
```

If no action is needed:

```jsx
<View style={styles.header}>
  <TouchableOpacity onPress={handleBack}>
    <Text style={styles.headerAction}>🔙 חזור</Text>
  </TouchableOpacity>

  <Text style={styles.headerTitle}>📑 סקרים</Text>

  <View style={styles.headerAction} />
</View>
```

---

### 🎨 Styling for React Native

```js
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#667eea',
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 18,
    color: 'white',
  },
  headerAction: {
    color: 'white',
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
});
```

---

### 📌 Usage Tips

- Use this layout across mobile and web views.
- Keep icons consistent: 📑 (Survey), 📅 (Events), 🏠 (Home), etc.
- For accessibility: add `accessibilityLabel` to `TouchableOpacity` components.
- Use `I18nManager.isRTL` to conditionally style if supporting both LTR and RTL.


