{"dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-picker/picker": "2.11.0", "expo-image-picker": "~16.1.4", "expo-localization": "~16.1.5", "expo-notifications": "~0.31.3", "expo-status-bar": "~2.2.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-native": "0.80.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "zod": "^3.25.64"}, "devDependencies": {"@types/react": "~19.0.10", "eslint-config-expo": "~9.2.0"}}