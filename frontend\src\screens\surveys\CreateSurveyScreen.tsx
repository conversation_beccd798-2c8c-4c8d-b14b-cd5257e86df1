import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView, Icon } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { surveysService } from '@services/surveys';
import { SurveyQuestion, CreateSurveyData } from '../../types/survey';
import { useAuth } from '@contexts/AuthContext';
import { Input } from '../../components/paper/Input';
import { SimpleCalendar } from '../../components/SimpleCalendar';
import { TextInput } from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';

type CreateSurveyScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'CreateSurvey'>;

export default function CreateSurveyScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateSurveyScreenNavigationProp>();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [image, setImage] = useState<string | null>(null);

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  // Track which fields have been touched for validation
  const [touchedFields, setTouchedFields] = useState({
    title: false,
    description: false,
  });

  const [formData, setFormData] = useState<{
    title: string;
    description: string;
    closingDate: string;
    imageUrl: string;
    questions: SurveyQuestion[];
  }>({
    title: '',
    description: '',
    closingDate: '',
    imageUrl: '',
    questions: [
      {
        id: '1',
        text: '',
        type: 'single-choice',
        options: [''],
        required: true,
      }
    ],
  });

  // Check if a field is invalid (empty and touched)
  const isFieldInvalid = (fieldName: keyof typeof touchedFields) => {
    return touchedFields[fieldName] && !formData[fieldName];
  };

  // Check if form is valid
  const isFormValid = () => {
    return formData.title && 
           formData.description && 
           formData.questions.length > 0 && 
           formData.questions.every(q => q.text && q.options?.every(o => o));
  };

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return date.toLocaleDateString('he-IL', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // Handle date selection
  const handleDateSelect = (selectedDate: Date) => {
    setSelectedDate(selectedDate);
    const formattedDate = selectedDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    setFormData({ ...formData, closingDate: formattedDate });
    setShowDatePicker(false);
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      console.log('❌ Image picker permission denied');
      Alert.alert('Permission needed', 'Please grant permission to access your photos');
      return;
    }

    console.log('🔄 Opening image picker');
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      console.log('✅ Image selected:', {
        uri: result.assets[0].uri,
        width: result.assets[0].width,
        height: result.assets[0].height,
        type: result.assets[0].type
      });
      setImage(result.assets[0].uri);
      setFormData(prev => ({ ...prev, imageUrl: result.assets[0].uri }));
    } else {
      console.log('ℹ️ Image selection canceled');
    }
  };

  const handleSubmit = async () => {
    if (!isFormValid()) {
      console.log('❌ Survey validation failed:', {
        hasTitle: !!formData.title,
        hasDescription: !!formData.description,
        questionsCount: formData.questions.length,
        questionsValid: formData.questions.every(q => q.text && q.options?.every(o => o))
      });
      Alert.alert('שגיאה', 'אנא מלא את כל השדות החובה');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Starting survey creation:', {
        title: formData.title,
        description: formData.description,
        questionsCount: formData.questions.length,
        questions: formData.questions,
        hasImage: !!image
      });

      const surveyFormData = new FormData();
      
      surveyFormData.append('title', formData.title);
      surveyFormData.append('description', formData.description);
      surveyFormData.append('closingDate', formData.closingDate);
      surveyFormData.append('questions', JSON.stringify(formData.questions));
      
      if (image) {
        const response = await fetch(image);
        const blob = await response.blob();
        
        const filename = image.split('/').pop() ?? 'image.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';
        
        console.log('📸 Preparing image for upload:', {
          filename,
          type,
          uri: image
        });

        const imageFile = new File([blob], filename, { type });
        
        surveyFormData.append('image', imageFile);
        console.log('✅ Image prepared and added to form data');
      }

      const response = await surveysService.createSurveyViaBackend(surveyFormData);
      
      if (response.data) {
        console.log('✅ Survey created successfully:', {
          id: response.data.id,
          title: response.data.title,
          hasImage: !!response.data.imageUrl
        });
        Alert.alert(
          'הצלחה',
          `הסקר "${response.data.title}" נוצר בהצלחה! 🎉`,
          [
            {
              text: 'אישור',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        console.error('❌ Failed to create survey:', response.error);
        Alert.alert('שגיאה', 'שגיאה ביצירת הסקר');
      }
    } catch (error) {
      console.error('❌ Error creating survey:', error);
      Alert.alert('שגיאה', 'שגיאה ביצירת הסקר');
    } finally {
      setLoading(false);
    }
  };

  const addQuestion = () => {
    console.log('➕ Adding new question');
    const newQuestion: SurveyQuestion = {
      id: Date.now().toString(),
      text: '',
      type: 'single-choice',
      options: [''],
      required: true,
    };
    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
  };

  const removeQuestion = (index: number) => {
    console.log('➖ Removing question at index:', index);
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
  };

  const updateQuestion = (index: number, field: keyof SurveyQuestion, value: any) => {
    console.log('📝 Updating question:', {
      index,
      field,
      value
    });
    setFormData(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === index ? { ...q, [field]: value } : q
      )
    }));
  };

  const addOption = (questionIndex: number) => {
    const updatedQuestions = [...formData.questions];
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options!.push('');
      setFormData(prev => ({
        ...prev,
        questions: updatedQuestions
      }));
    }
  };

  const updateOption = (questionIndex: number, optionIndex: number, value: string) => {
    const updatedQuestions = [...formData.questions];
    if (updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options![optionIndex] = value;
      setFormData(prev => ({
        ...prev,
        questions: updatedQuestions
      }));
    }
  };

  const removeOption = (questionIndex: number, optionIndex: number) => {
    const updatedQuestions = [...formData.questions];
    if (updatedQuestions[questionIndex].options && updatedQuestions[questionIndex].options!.length > 1) {
      updatedQuestions[questionIndex].options!.splice(optionIndex, 1);
      setFormData(prev => ({
        ...prev,
        questions: updatedQuestions
      }));
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="📝 יצירת סקר"
        showBackButton={true}
        rightComponent={
          <TouchableOpacity
            style={[styles.saveButton, !isFormValid() && styles.saveButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading || !isFormValid()}
          >
            <Text style={styles.saveButtonText}>
              {loading ? 'שומר...' : 'שמור'}
            </Text>
          </TouchableOpacity>
        }
      />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Basic Information */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Input
              label="כותרת הסקר *"
              value={formData.title}
              onChangeText={(text: string) => {
                setFormData({ ...formData, title: text });
                setTouchedFields({ ...touchedFields, title: true });
              }}
              placeholder="לדוגמה: מה חשוב לכם במרכז הקהילתי?"
              error={isFieldInvalid('title') ? '' : undefined}
              outlineColor={getColor('neutral', 400)}
              activeOutlineColor={getColor('primary')}
            />
          </View>

          <View style={styles.inputGroup}>
            <Input
              label="תיאור *"
              value={formData.description}
              onChangeText={(text: string) => {
                setFormData({ ...formData, description: text });
                setTouchedFields({ ...touchedFields, description: true });
              }}
              placeholder="תאר את מטרת הסקר..."
              error={isFieldInvalid('description') ? '' : undefined}
              outlineColor={getColor('neutral', 400)}
              activeOutlineColor={getColor('primary')}
            />
          </View>
        </View>

        {/* Questions Section */}
        <View style={styles.questionsSection}>
          <Text style={[styles.sectionTitle, { textAlign: getTextAlign() }]}>שאלות הסקר</Text>
          {formData.questions.map((question, questionIndex) => (
            <View key={question.id} style={styles.questionCard}>
              <View style={styles.questionHeader}>
                <Text style={[styles.questionNumber, { textAlign: 'right', writingDirection: 'rtl' }]}>שאלה {questionIndex + 1}</Text>
                {questionIndex > 0 && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeQuestion(questionIndex)}
                  >
                    <Icon name="close" size={20} color={getColor('error')} />
                  </TouchableOpacity>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Input
                  label="שאלה *"
                  value={question.text}
                  onChangeText={(text: string) => updateQuestion(questionIndex, 'text', text)}
                  placeholder="הקלד את השאלה..."
                  error={!question.text ? '' : undefined}
                  outlineColor={getColor('neutral', 400)}
                  activeOutlineColor={getColor('primary')}
                />
              </View>

              {question.type === 'single-choice' && (
                <View style={styles.optionsContainer}>
                  {question.options?.map((option, optionIndex) => (
                    <View key={optionIndex} style={styles.optionInput}>
                      <Input
                        label={`אפשרות ${optionIndex + 1} *`}
                        value={option}
                        onChangeText={(text: string) => updateOption(questionIndex, optionIndex, text)}
                        placeholder={`אפשרות ${optionIndex + 1}`}
                        error={!option ? '' : undefined}
                        outlineColor={getColor('neutral', 400)}
                        activeOutlineColor={getColor('primary')}
                        right={question.options!.length > 1 ? (
                          <TextInput.Icon 
                            icon="close" 
                            onPress={() => removeOption(questionIndex, optionIndex)}
                            color={getColor('error')}
                          />
                        ) : undefined}
                      />
                    </View>
                  ))}
                  <TouchableOpacity
                    style={styles.addOptionButton}
                    onPress={() => addOption(questionIndex)}
                  >
                    <Icon name="plus" size={20} color={getColor('primary')} />
                    <Text style={[styles.addOptionText, { textAlign: 'right', writingDirection: 'rtl' }]}>הוסף אפשרות</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          ))}

          <TouchableOpacity
            style={styles.addQuestionButton}
            onPress={addQuestion}
          >
            <Icon name="plus" size={20} color={getColor('white')} />
            <Text style={styles.addQuestionText}>הוסף שאלה</Text>
          </TouchableOpacity>
        </View>

        {/* Additional Settings */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>תאריך סיום</Text>
            <TouchableOpacity
              onPress={() => setShowDatePicker(true)}
              style={[styles.inputContainer, styles.datePickerButton]}
              activeOpacity={0.7}
            >
              <Text style={formData.closingDate ? styles.dateText : styles.datePlaceholder}>
                {formData.closingDate ? formData.closingDate : 'YYYY-MM-DD (אופציונלי)'}
              </Text>
              <Icon name="event" size={24} color="#666" style={{ position: 'absolute', left: 12, top: '50%', marginTop: -10 }} />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>תמונה (אופציונלי)</Text>
            <View style={styles.imageUploadContainer}>
              <TouchableOpacity style={styles.imageUploadButton} onPress={pickImage}>
                {image ? (
                  <Image source={{ uri: image }} style={styles.uploadedImage} />
                ) : (
                  <View style={styles.uploadPlaceholder}>
                    <Icon
                      name="add"
                      size={24}
                      color="#666"
                    />
                    <Text style={styles.uploadText}>הוסף תמונה</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {showDatePicker && (
          <SimpleCalendar
            visible={showDatePicker}
            onDateSelect={handleDateSelect}
            onClose={() => setShowDatePicker(false)}
            selectedDate={selectedDate}
          />
        )}
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: getColor('neutral', 100),
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: getSpacing('lg'),
  },
  formSection: {
    gap: getSpacing('lg'),
  },
  inputGroup: {
    gap: getSpacing('xs'),
  },
  questionsSection: {
    marginTop: getSpacing('lg'),
  },
  sectionTitle: {
    ...getTypography('lg', 'bold'),
    color: getColor('neutral', 700),
    marginBottom: getSpacing('md'),
  },
  questionCard: {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    marginBottom: getSpacing('md'),
    ...getShadow('sm'),
  },
  questionHeader: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getSpacing('md'),
  },
  questionNumber: {
    ...getTypography('base', 'bold'),
    color: getColor('neutral', 700),
    textAlign: 'right',
  },
  removeButton: {
    padding: getSpacing('xs'),
  },
  optionsContainer: {
    gap: getSpacing('md') * 0.75,
    marginTop: getSpacing('md') * 0.75,
  },
  optionInput: {
  },
  addOptionButton: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    gap: getSpacing('xs'),
    padding: getSpacing('sm'),
    borderRadius: getBorderRadius('md'),
    backgroundColor: getColor('neutral', 100),
  },
  addOptionText: {
    ...getTypography('base', 'medium'),
    color: getColor('primary'),
    textAlign: 'right',
  },
  addQuestionButton: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    gap: getSpacing('xs'),
    padding: getSpacing('md'),
    borderRadius: getBorderRadius('lg'),
    backgroundColor: getColor('primary'),
    marginTop: getSpacing('lg'),
    width: 320,
    alignSelf: 'flex-end',
    marginBottom: getSpacing('xl'),
  },
  addQuestionText: {
    ...getTypography('base', 'bold'),
    color: getColor('white'),
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  saveButton: {
    backgroundColor: getColor('primary'),
    paddingHorizontal: getSpacing('lg'),
    paddingVertical: getSpacing('sm'),
    borderRadius: getBorderRadius('full'),
  },
  saveButtonDisabled: {
    backgroundColor: getColor('neutral', 400),
    opacity: 0.7,
  },
  saveButtonText: {
    ...getTypography('base', 'bold'),
    color: getColor('white'),
  },
  datePickerButton: {
    width: '100%',
  },
  label: {
    color: getColor('neutral', 700),
    fontWeight: '600',
    marginBottom: getSpacing('xs'),
    fontSize: 14,
    textAlign: 'right',
  },
  dateText: {
    color: getColor('neutral', 900),
    fontSize: 16,
    textAlign: 'right',
    flex: 1,
    paddingVertical: getSpacing('sm'),
    paddingHorizontal: getSpacing('md'),
  },
  datePlaceholder: {
    color: getColor('neutral', 400),
    fontSize: 16,
    textAlign: 'right',
    flex: 1,
    paddingVertical: getSpacing('sm'),
    paddingHorizontal: getSpacing('md'),
  },
  inputContainer: {
    width: '100%',
    borderWidth: 1,
    borderColor: getColor('neutral', 300),
    borderRadius: getBorderRadius('md'),
    padding: getSpacing('md'),
  },
  imageUploadContainer: {
    alignItems: 'flex-end',
  },
  imageUploadButton: {
    width: 80,
    height: 80,
    borderRadius: getBorderRadius('full'),
    overflow: 'hidden',
    backgroundColor: getColor('neutral', 50),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: getColor('neutral', 200),
    borderStyle: 'dashed',
  },
  uploadedImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  uploadPlaceholder: {
    alignItems: 'center',
    gap: getSpacing('xs'),
  },
  uploadText: {
    ...getTypography('xs'),
    color: getColor('neutral', 400),
  },
});
