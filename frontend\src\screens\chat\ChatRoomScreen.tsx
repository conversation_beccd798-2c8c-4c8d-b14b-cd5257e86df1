import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import { chatService, ChatMessage } from '../../services/chat';
import { ChatStackParamList } from '../../navigation/types';
import Header from '../../components/Header';
import { useAuth } from '../../contexts/AuthContext';
import { ChatInput } from '../../components/ChatInput';

type ChatRoomScreenRouteProp = RouteProp<ChatStackParamList, 'ChatRoom'>;

const mockMessages = [
  {
    id: '1',
    text: 'שלום לכולם! מתי האסיפה הבאה?',
    senderId: 'user1',
    senderName: 'דני כהן',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    isOwn: false,
    groupId: '1',
    avatarUrl: 'D',
    avatarDataAiHint: null,
  },
  {
    id: '2',
    text: 'היי דני, האסיפה תהיה ביום רביעי בשעה 19:00',
    senderId: 'current',
    senderName: 'אני',
    timestamp: new Date(Date.now() - 28 * 60 * 1000), // 28 minutes ago
    isOwn: true,
    groupId: '1',
    avatarUrl: 'א',
    avatarDataAiHint: null,
  },
  {
    id: '3',
    text: 'תודה רבה!',
    senderId: 'user1',
    senderName: 'דני כהן',
    timestamp: new Date(Date.now() - 27 * 60 * 1000), // 27 minutes ago
    isOwn: false,
    groupId: '1',
    avatarUrl: 'D',
    avatarDataAiHint: null,
  },
  {
    id: '4',
    text: 'גם אני אגיע. יש צורך להביא משהו?',
    senderId: 'user2',
    senderName: 'מריה רודריגז',
    timestamp: new Date(Date.now() - 25 * 60 * 1000), // 25 minutes ago
    isOwn: false,
    groupId: '1',
    avatarUrl: 'M',
    avatarDataAiHint: null,
  },
  {
    id: '5',
    text: 'לא צריך להביא כלום, רק את עצמכם 😊',
    senderId: 'current',
    senderName: 'אני',
    timestamp: new Date(Date.now() - 20 * 60 * 1000), // 20 minutes ago
    isOwn: true,
    groupId: '1',
    avatarUrl: 'א',
    avatarDataAiHint: null,
  },
];

export default function ChatRoomScreen() {
  const { t } = useTranslation();
  const route = useRoute<ChatRoomScreenRouteProp>();
  const navigation = useNavigation();
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [groupName, setGroupName] = useState('צ\'אט');
  const [memberCount, setMemberCount] = useState(0);

  const { groupId } = route.params;

  console.log('🏠 ChatRoomScreen initialized with groupId:', groupId);
  console.log('🔍 groupId type:', typeof groupId);
  console.log('🔍 groupId value:', JSON.stringify(groupId));

  useEffect(() => {
    loadMessages();
    loadGroupInfo();
  }, [groupId]);

  const loadMessages = async () => {
    try {
      console.log('🔄 Loading messages for group:', groupId);
      const fetchedMessages = await chatService.getChatMessages(groupId);
      setMessages(fetchedMessages);
      console.log('✅ Messages loaded successfully:', fetchedMessages.length);
    } catch (error) {
      console.error('❌ Error loading messages:', error);
      // Fallback to mock data if API fails
      setMessages(mockMessages);
      console.log('⚠️ Using mock data due to API failure');
    } finally {
      setLoading(false);
    }
  };

  const loadGroupInfo = async () => {
    try {
      console.log('🔄 Loading group info for group:', groupId);
      const group = await chatService.getChatGroup(groupId);
      console.log('✅ Group info loaded:', {
        name: group.name,
        memberCount: group.members?.length || 0,
        members: group.members
      });

      setGroupName(group.name);
      setMemberCount(group.members?.length || 0);
    } catch (error) {
      console.error('❌ Error loading group info:', error);
      setGroupName('קבוצת הורים'); // Fallback name
      setMemberCount(0); // Fallback count
    }
  };

  const formatTime = (timestamp: Date | string) => {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const renderMessage = ({ item }: { item: any }) => {
    const timeDisplay = formatTime(item.timestamp);

    return (
      <View style={styles.messageWrapper}>
        <View style={[
          styles.messageContainer,
          item.isOwn ? styles.ownMessage : styles.otherMessage
        ]}>
          <View style={[
            styles.messageBubble,
            item.isOwn ? styles.ownMessageBubble : styles.otherMessageBubble
          ]}>
            {!item.isOwn && (
              <Text style={styles.senderNameInBubble}>
                <Text style={styles.senderNameBold}>{item.senderName}:</Text> {item.text}
              </Text>
            )}
            {item.isOwn && (
              <Text style={[
                styles.messageText,
                { textAlign: getTextAlign() },
                item.isOwn ? styles.ownMessageText : styles.otherMessageText
              ]}>
                {item.text}
              </Text>
            )}
          </View>
          <Text style={[
            styles.messageTime,
            item.isOwn ? styles.ownMessageTime : styles.otherMessageTime
          ]}>
            {timeDisplay}
          </Text>
        </View>
      </View>
    );
  };

  const sendMessage = async () => {
    if (message.trim() && user) {
      try {
        console.log('�� Sending message to group:', groupId);
        console.log('📝 Message content:', message.trim());
        console.log('👤 User:', user.id, user.first_name, user.last_name);

        const newMessage = {
          groupId: groupId,
          senderId: user.id,
          senderName: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'משתמש',
          text: message.trim(),
          avatarUrl: `${(user.first_name?.[0] || '')}${(user.last_name?.[0] || '')}`.toUpperCase() || '?',
          avatarDataAiHint: null,
        };

        console.log('🔍 Message object:', newMessage);

        // Pass groupId as first parameter, message object as second
        await chatService.sendMessage(groupId, newMessage);

        console.log('✅ Message sent successfully');
        setMessage('');
        // Reload messages to show the new one
        loadMessages();
      } catch (error) {
        console.error('❌ Error sending message:', error);
        // For now, just clear the input even if sending fails
        setMessage('');
      }
    } else if (!user) {
      console.error('❌ No authenticated user found');
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען הודעות...</Text>
      </View>
    );
  }

  const headerRightComponent = (
    <View style={styles.chatActions}>
      <TouchableOpacity style={styles.chatAction}>
        <Text style={styles.chatActionText}>📞</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.chatAction}>
        <Text style={styles.chatActionText}>⋯</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Custom Chat Header */}
      <View style={styles.chatHeader}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <View style={styles.chatAvatar}>
          <Text style={styles.chatAvatarText}>👨‍👩‍👧‍👦</Text>
        </View>
        <View style={styles.chatInfo}>
          <Text style={styles.chatName}>{groupName}</Text>
          <Text style={styles.chatStatus}>
            {memberCount === 0 ? 'טוען...' :
             memberCount === 1 ? 'חבר אחד' :
             `${memberCount} חברים`}
          </Text>
        </View>
        {headerRightComponent}
      </View>

      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
        inverted
        showsVerticalScrollIndicator={false}
      />

      {/* Message Input */}
      <ChatInput
        value={message}
        onChangeText={setMessage}
        onSend={sendMessage}
        placeholder="הקלד הודעה..."
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Custom Chat Header
  chatHeader: {
    backgroundColor: '#667eea',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: Platform.OS === 'android' ? 35 : 15,
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  chatAvatar: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  chatAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  chatInfo: {
    flex: 1,
  },
  chatName: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  chatStatus: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
  },
  chatActions: {
    flexDirection: 'row',
    gap: 15,
  },
  chatAction: {
    padding: 5,
  },
  chatActionText: {
    color: 'white',
    fontSize: 18,
  },
  // Messages
  messagesList: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: '#f8f9fa',
  },
  messagesContainer: {
    paddingVertical: 10,
  },
  messageWrapper: {
    marginVertical: 8,
  },
  messageContainer: {
    maxWidth: '80%',
    flexDirection: 'column',
  },
  ownMessage: {
    alignSelf: 'flex-end',
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
  },
  messageBubble: {
    padding: 12,
    borderRadius: 18,
    marginBottom: 4,
  },
  ownMessageBubble: {
    backgroundColor: '#667eea',
    borderBottomRightRadius: 6,
  },
  otherMessageBubble: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e1e8ed',
    borderBottomLeftRadius: 6,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 18,
  },
  ownMessageText: {
    color: 'white',
  },
  otherMessageText: {
    color: '#2c3e50',
  },
  senderNameInBubble: {
    fontSize: 14,
    lineHeight: 18,
    color: '#2c3e50',
  },
  senderNameBold: {
    fontWeight: 'bold',
  },
  messageTime: {
    fontSize: 11,
    color: '#95a5a6',
    marginTop: 4,
  },
  ownMessageTime: {
    color: '#95a5a6',
  },
  otherMessageTime: {
    color: '#95a5a6',
  },
  // Input Container
  inputContainer: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: 'white',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e1e8ed',
    gap: 10,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e1e8ed',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontSize: 14,
    backgroundColor: 'white',
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  sendButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
