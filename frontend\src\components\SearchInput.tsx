import React from 'react';
import { StyleSheet } from 'react-native';
import { TextInput } from 'react-native-paper';
import { Input } from './paper/Input';
import { getColor } from '../theme';

interface SearchInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  style?: any;
}

export const SearchInput = ({
  value,
  onChangeText,
  placeholder,
  style,
}: SearchInputProps) => {
  return (
    <Input
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      style={[styles.searchInput, style]}
      outlineColor="#94a3b8"
      activeOutlineColor="#4A90E2"
    />
  );
};

const styles = StyleSheet.create({
  searchInput: {
    marginVertical: 4,
  },
}); 