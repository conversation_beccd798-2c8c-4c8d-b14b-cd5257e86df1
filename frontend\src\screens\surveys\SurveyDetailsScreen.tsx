import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '@utils/rtl';
import { Header, WebCompatibleScrollView } from '@components';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '@utils';
import { getSpacing, getColor, getBorderRadius, getShadow, getTypography } from '../../theme';
import { SurveysStackParamList } from '@navigation/types';
import { Survey, surveysService } from '@services/surveys';

type SurveyDetailsScreenRouteProp = RouteProp<SurveysStackParamList, 'SurveyDetails'>;
type SurveyDetailsScreenNavigationProp = StackNavigationProp<SurveysStackParamList, 'SurveyDetails'>;

export default function SurveyDetailsScreen() {
  const { t } = useTranslation();
  const route = useRoute<SurveyDetailsScreenRouteProp>();
  const navigation = useNavigation<SurveyDetailsScreenNavigationProp>();
  const { surveyId } = route.params;

  const [survey, setSurvey] = useState<Survey | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSurvey();
  }, [surveyId]);

  const loadSurvey = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading survey details for ID:', surveyId);
      const surveyData = await surveysService.getSurveyById(surveyId);

      if (surveyData) {
        setSurvey(surveyData);
        console.log('✅ Survey loaded successfully:', surveyData.title);
      } else {
        Alert.alert('שגיאה', 'הסקר לא נמצא');
      }
    } catch (error) {
      console.error('❌ Error loading survey:', error);
      Alert.alert('שגיאה', 'שגיאה בטעינת פרטי הסקר');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const handleParticipate = () => {
    if (survey?.status === 'OPEN') {
      // Navigate to answer survey screen
      navigation.navigate('SurveyResults', { surveyId: survey.id });
    } else {
      Alert.alert('סקר סגור', 'הסקר הזה כבר נסגר ולא ניתן להשתתף בו');
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען פרטי סקר...</Text>
      </View>
    );
  }

  if (!survey) {
    return (
      <View style={styles.centerContainer}>
        <Text>הסקר לא נמצא</Text>
      </View>
    );
  }

  const isOpen = survey.status === 'OPEN';

  return (
    <View style={styles.container}>
      <Header
        title={`🏗️ ${survey.title}`}
        showBackButton={true}
      />
      <WebCompatibleScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* Survey Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: survey.imageUrl || 'https://via.placeholder.com/300x150?text=Survey+Image' }}
            style={styles.surveyImage}
          />
        </View>

        {/* Survey Title */}
        <Text style={[styles.surveyTitle, { textAlign: getTextAlign() }]}>
          {survey.title}
        </Text>

        {/* Survey Description */}
        <Text style={[styles.surveyDescription, { textAlign: getTextAlign() }]}>
          {survey.description}
        </Text>

        {/* Survey Info */}
        <Text style={[styles.surveyInfo, { textAlign: getTextAlign() }]}>
          🗓️ סיום הסקר: <Text style={styles.surveyInfoBold}>
            {survey.closingDate ? formatDate(survey.closingDate) : '30 ביוני 2025'}
          </Text>{'\n'}
          👥 משתתפים עד כה: <Text style={styles.surveyInfoBold}>
            {survey.participantsCount || 142}
          </Text>
        </Text>

        {/* Action Button */}
        <TouchableOpacity
          style={[styles.participateButton]}
          onPress={handleParticipate}
          disabled={!isOpen}
        >
          <Text style={[styles.participateButtonText, { textAlign: getTextAlign() }]}>
            השתתפו עכשיו
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle({
    flex: 1,
    backgroundColor: getColor('background'),
  }),
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: getColor('background'),
  },
  content: {
    flex: 1,
  },
  scrollContent: createWebCompatibleScrollContentStyle({
    flexGrow: 1,
    padding: 20,
  }),
  imageContainer: {
    textAlign: 'center',
    marginBottom: 15,
  },
  surveyImage: {
    width: '100%',
    height: 150,
    borderRadius: 12,
    resizeMode: 'cover',
  },
  surveyTitle: {
    color: '#2c3e50',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  surveyDescription: {
    color: '#7f8c8d',
    fontSize: 14,
    marginBottom: 15,
    lineHeight: 20,
  },
  surveyInfo: {
    fontSize: 13,
    color: '#95a5a6',
    marginBottom: 10,
    lineHeight: 18,
  },
  surveyInfoBold: {
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  participateButton: {
    backgroundColor: '#667eea',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    width: '100%',
  },
  participateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
