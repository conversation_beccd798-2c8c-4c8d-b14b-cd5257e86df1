import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { EventsStackParamList } from '../../navigation/types';
import { getTextAlign } from '../../utils/rtl';
import { eventsService } from '../../services/events';
import Header from '../../components/Header';
import { IconPicker } from '../../components/IconPicker';
import { SimpleCalendar } from '../../components/SimpleCalendar';
import { CrossPlatformTimePicker } from '../../components/CrossPlatformCalendar';
import { Input } from '../../components/paper/Input';

type CreateEventScreenNavigationProp = StackNavigationProp<EventsStackParamList, 'CreateEvent'>;

export default function CreateEventScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateEventScreenNavigationProp>();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    date: '',
    time: '',
    type: 'tenant' as 'community' | 'tenant',
    category: 'Social Gathering',
    ageGroup: 'All Ages',
    icon: '📅', // Default calendar icon
  });

  // Date picker state
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Time picker state
  const [selectedTime, setSelectedTime] = useState<Date | null>(null);
  const [showTimePicker, setShowTimePicker] = useState(false);

  // Track which fields have been touched for validation
  const [touchedFields, setTouchedFields] = useState({
    title: false,
    description: false,
    location: false,
    date: false,
    time: false,
  });

  // Check if a field is invalid (empty and touched)
  const isFieldInvalid = (fieldName: keyof typeof touchedFields) => {
    return touchedFields[fieldName] && !formData[fieldName];
  };

  // Check if form is valid
  const isFormValid = () => {
    return formData.title && formData.description && formData.location && formData.date && formData.time;
  };

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return date.toLocaleDateString('he-IL', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // Format time for display
  const formatTimeForDisplay = (date: Date) => {
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Handle date selection
  const handleDateSelect = (selectedDate: Date) => {
    setSelectedDate(selectedDate);
    const formattedDate = selectedDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    setFormData({ ...formData, date: formattedDate });
    setTouchedFields({ ...touchedFields, date: true });
    setShowDatePicker(false);
  };

  // Handle time selection
  const handleTimeSelect = (selectedTime: Date) => {
    setSelectedTime(selectedTime);
    const formattedTime = selectedTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    setFormData({ ...formData, time: formattedTime });
    setTouchedFields({ ...touchedFields, time: true });
    setShowTimePicker(false);
  };

  const handleCreateEvent = async () => {
    console.log('🔄 handleCreateEvent called');
    console.log('🔄 Form data:', formData);

    if (!formData.title || !formData.description || !formData.date || !formData.time || !formData.location) {
      console.log('❌ Missing required fields');
      Alert.alert('שגיאה', 'אנא מלא את כל השדות הנדרשים (כותרת, תיאור, תאריך, שעה, מיקום)');
      return;
    }

    console.log('✅ Validation passed, creating event...');
    setLoading(true);
    try {
      console.log('🔄 Creating event:', formData);

      // Create event data - combine date and time
      let eventDate = new Date();
      if (formData.date && formData.time) {
        // Parse the date and time strings
        const [year, month, day] = formData.date.split('-').map(Number);
        const [hours, minutes] = formData.time.split(':').map(Number);
        
        // Create a new date object with the parsed values
        eventDate = new Date(year, month - 1, day, hours, minutes);
        
        // Validate the date
        if (isNaN(eventDate.getTime())) {
          throw new Error('Invalid date or time format');
        }
      }

      const eventData = {
        title: formData.title,
        description: formData.description,
        location: formData.location || '',
        date: eventDate,
        type: formData.type,
        icon: formData.icon,
        category: formData.category,
        ageGroup: formData.ageGroup,
      };

      const newEvent = await eventsService.createEvent(eventData);
      console.log('✅ Event created successfully:', newEvent);

      Alert.alert(
        'הצלחה',
        `האירוע "${newEvent.title}" נוצר בהצלחה! 🎉`,
        [
          {
            text: 'אישור',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('❌ Error creating event:', error);
      Alert.alert(
        'שגיאה',
        'שגיאה ביצירת האירוע. אנא נסה שוב.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="➕ יצירת אירוע חדש"
        showBackButton={true}
      />
      <ScrollView style={styles.content}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          {t('events.createEvent')}
        </Text>

        <View style={styles.form}>
          {/* Event Title */}
          <View style={styles.formGroup}>
            <Input
              label="כותרת האירוע *"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
              placeholder="לדוגמה: מסיבת ברביקיו קיץ"
              error={isFieldInvalid('title') ? 'שדה חובה' : undefined}
            />
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Input
              label="תיאור *"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              placeholder="ספר לכולם על האירוע שלך..."
              multiline
              numberOfLines={4}
              error={isFieldInvalid('description') ? 'שדה חובה' : undefined}
            />
          </View>

          {/* Date and Time Row */}
          <View style={styles.formRow}>
            <View style={styles.compactFormGroup}>
              <TouchableOpacity
                style={[
                  styles.datePickerButton,
                  isFieldInvalid('date') && styles.formInputError
                ]}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.calendarIcon}>📅</Text>
                <Text style={[
                  styles.datePickerText,
                  !formData.date && styles.placeholderText
                ]}>
                  {selectedDate ? formatDateForDisplay(selectedDate) : 'בחר תאריך'}
                </Text>
              </TouchableOpacity>
              {isFieldInvalid('date') && (
                <Text style={styles.errorText}>שדה חובה</Text>
              )}
            </View>
            <View style={styles.compactFormGroup}>
              <TouchableOpacity
                style={[
                  styles.timePickerButton,
                  isFieldInvalid('time') && styles.formInputError
                ]}
                onPress={() => setShowTimePicker(true)}
              >
                <Text style={styles.clockIcon}>🕐</Text>
                <Text style={[
                  styles.timePickerText,
                  !formData.time && styles.placeholderText
                ]}>
                  {selectedTime ? formatTimeForDisplay(selectedTime) : 'בחר שעה'}
                </Text>
              </TouchableOpacity>
              {isFieldInvalid('time') && (
                <Text style={styles.errorText}>שדה חובה</Text>
              )}
            </View>
          </View>

          {/* Location */}
          <View style={styles.formGroup}>
            <Input
              label="מיקום *"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
              placeholder="לדוגמה: פארק מרכזי, מרכז קהילתי"
              error={isFieldInvalid('location') ? 'שדה חובה' : undefined}
            />
          </View>

          {/* Category */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>קטגוריה</Text>
            <View style={styles.selectContainer}>
              <Text style={styles.selectText}>{formData.category}</Text>
            </View>
          </View>

          {/* Age Group */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>קבוצת גיל</Text>
            <View style={styles.selectContainer}>
              <Text style={styles.selectText}>{formData.ageGroup}</Text>
            </View>
          </View>

          {/* Icon Selection */}
          <IconPicker
            selectedIcon={formData.icon}
            onIconSelect={(icon) => setFormData({ ...formData, icon })}
            label="אייקון האירוע"
          />

          <View style={styles.typeContainer}>
            <Text style={[styles.typeLabel, { textAlign: getTextAlign() }]}>
              {t('events.eventType')}:
            </Text>
            <View style={styles.typeButtons}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.type === 'community' && styles.typeButtonActive
                ]}
                onPress={() => setFormData({ ...formData, type: 'community' })}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.type === 'community' && styles.typeButtonTextActive
                ]}>
                  {t('events.communityEvent')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.type === 'tenant' && styles.typeButtonActive
                ]}
                onPress={() => setFormData({ ...formData, type: 'tenant' })}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.type === 'tenant' && styles.typeButtonTextActive
                ]}>
                  {t('events.tenantEvent')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.createButton,
              (loading || !isFormValid()) && styles.createButtonDisabled
            ]}
            onPress={handleCreateEvent}
            disabled={loading || !isFormValid()}
          >
            <Text style={styles.createButtonText}>
              {loading ? 'יוצר אירוע...' : 'צור אירוע'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Simple Calendar Date Picker */}
      <SimpleCalendar
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateSelect={handleDateSelect}
        selectedDate={selectedDate || undefined}
      />

      {/* Time Picker */}
      <CrossPlatformTimePicker
        visible={showTimePicker}
        onClose={() => setShowTimePicker(false)}
        onTimeSelect={handleTimeSelect}
        selectedTime={selectedTime || undefined}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  form: {
    width: '100%',
  },
  formGroup: {
    marginBottom: 20,
  },
  compactFormGroup: {
    marginBottom: 8,
    flex: 1,
  },
  formRow: {
    flexDirection: 'row-reverse',
    gap: 15,
  },
  datePickerButton: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timePickerButton: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calendarIcon: {
    fontSize: 20,
  },
  clockIcon: {
    fontSize: 20,
  },
  datePickerText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  timePickerText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  placeholderText: {
    color: '#95a5a6',
  },
  selectContainer: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    backgroundColor: 'white',
  },
  selectText: {
    fontSize: 14,
    color: '#2c3e50',
    textAlign: 'right',
  },
  formInputError: {
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'right',
  },
  typeContainer: {
    marginBottom: 20,
  },
  typeLabel: {
    fontSize: 16,
    marginBottom: 10,
    color: '#333',
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  typeButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    backgroundColor: 'white',
    alignItems: 'center',
  },
  typeButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  typeButtonText: {
    fontSize: 14,
    color: '#2c3e50',
  },
  typeButtonTextActive: {
    color: 'white',
  },
  createButton: {
    backgroundColor: '#007AFF',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  createButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  formLabel: {
    color: '#2c3e50',
    fontWeight: '600',
    marginBottom: 8,
    fontSize: 14,
    textAlign: 'right',
  },
});
