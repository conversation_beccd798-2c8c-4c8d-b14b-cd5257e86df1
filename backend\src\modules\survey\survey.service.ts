import prisma from '../../config/prismaClient';
import { Survey } from './survey.types';
import { minioStorageClient, BUCKETS, StorageFile } from '../../services/minio-storage.service';
import { logger } from '../../utils/logger';

export class SurveyService {
  constructor() {
    // Using centralized Prisma client
  }

  private validateImageFile(file: { buffer: Buffer; mimetype: string }): void {
    const bucketConfig = minioStorageClient.getBucketConfig(BUCKETS.SURVEY_IMAGES);
    
    if (!bucketConfig.allowedMimeTypes?.includes(file.mimetype)) {
      throw new Error(`Invalid image type. Allowed types: ${bucketConfig.allowedMimeTypes?.join(', ')}`);
    }
    if (bucketConfig.fileSizeLimit && file.buffer.length > bucketConfig.fileSizeLimit) {
      throw new Error(`Image size exceeds maximum limit of ${bucketConfig.fileSizeLimit / (1024 * 1024)}MB`);
    }
  }

  private async uploadImage(file: Express.Multer.File | { buffer: Buffer; filename: string; mimetype: string }): Promise<string> {
    try {
      logger.info('[SURVEY] Uploading image before creating survey');
      // Convert the file object to match StorageFile if needed
      const storageFile: StorageFile = 'fieldname' in file ? file : {
        fieldname: 'image',
        originalname: file.filename,
        encoding: '7bit',
        mimetype: file.mimetype,
        buffer: file.buffer,
        size: file.buffer.length,
        filename: file.filename
      };
      
      // Use the BUCKETS constant for consistency
      const bucketName = BUCKETS.SURVEY_IMAGES;
      logger.info(`[SURVEY] Using bucket: ${bucketName}`);
      
      return await minioStorageClient.uploadImage(storageFile, bucketName);
    } catch (error) {
      logger.error('[SURVEY] Error uploading image:', error);
      throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private mapToSurvey(prismaSurvey: any): Survey {
    return {
      id: prismaSurvey.id,
      title: prismaSurvey.title,
      description: prismaSurvey.description,
      questions: prismaSurvey.questions,
      status: prismaSurvey.status,
      createdBy: prismaSurvey.created_by,
      createdAt: prismaSurvey.created_at,
      communityId: prismaSurvey.community_id,
      participantsCount: prismaSurvey.participants_count,
      closingDate: prismaSurvey.closing_date,
      imageUrl: prismaSurvey.image_url,
      dataAiHint: prismaSurvey.data_ai_hint
    };
  }

  private mapToPrismaData(survey: Partial<Survey>): any {
    const data: any = {};
    if (survey.title !== undefined) data.title = survey.title;
    if (survey.description !== undefined) data.description = survey.description;
    if (survey.questions !== undefined) data.questions = survey.questions;
    if (survey.status !== undefined) data.status = survey.status;
    if (survey.createdBy !== undefined) data.created_by = survey.createdBy;
    if (survey.communityId !== undefined) data.community_id = survey.communityId;
    if (survey.participantsCount !== undefined) data.participants_count = survey.participantsCount;
    if (survey.closingDate !== undefined) data.closing_date = survey.closingDate;
    if (survey.imageUrl !== undefined) data.image_url = survey.imageUrl;
    if (survey.dataAiHint !== undefined) data.data_ai_hint = survey.dataAiHint;
    return data;
  }

  async createSurvey(survey: Omit<Survey, 'id'>, imageFile?: Express.Multer.File): Promise<Survey> {
    let imageUrl = survey.imageUrl;

    // Handle image upload if provided
    if (imageFile) {
      this.validateImageFile(imageFile);
      imageUrl = await this.uploadImage(imageFile);
    }

    const prismaData = {
      title: survey.title,
      description: survey.description,
      questions: survey.questions,
      status: survey.status,
      created_by: survey.createdBy,
      community_id: survey.communityId,
      participants_count: survey.participantsCount || 0,
      closing_date: survey.closingDate,
      image_url: imageUrl,
      data_ai_hint: survey.dataAiHint
    };

    const createdSurvey = await prisma.survey.create({
      data: prismaData
    });

    return this.mapToSurvey(createdSurvey);
  }

  async getSurveys(): Promise<Survey[]> {
    const surveys = await prisma.survey.findMany({
      orderBy: { created_at: 'desc' }
    });
    return surveys.map(survey => this.mapToSurvey(survey));
  }

  async getSurvey(id: string): Promise<Survey | null> {
    const survey = await prisma.survey.findUnique({
      where: { id }
    });
    return survey ? this.mapToSurvey(survey) : null;
  }

  async updateSurvey(id: string, data: Partial<Survey>): Promise<Survey | null> {
    const prismaData = this.mapToPrismaData(data);
    const survey = await prisma.survey.update({
      where: { id },
      data: prismaData
    });
    return this.mapToSurvey(survey);
  }

  async deleteSurvey(id: string): Promise<void> {
    await prisma.survey.delete({
      where: { id }
    });
  }

  async submitSurveyResponse(responseData: {
    surveyId: string;
    userId: string;
    answers: any;
    submittedAt: Date;
  }): Promise<any> {
    console.log('📝 Creating survey response:', {
      surveyId: responseData.surveyId,
      userId: responseData.userId,
      answersType: typeof responseData.answers,
      answers: responseData.answers
    });

    // Check if user already responded to this survey
    const existingResponse = await prisma.surveyResponse.findUnique({
      where: {
        survey_id_user_id: {
          survey_id: responseData.surveyId,
          user_id: responseData.userId
        }
      }
    });

    if (existingResponse) {
      const error = new Error('User has already responded to this survey');
      (error as any).statusCode = 409; // Conflict
      (error as any).code = 'DUPLICATE_RESPONSE';
      throw error;
    }

    // Check if survey exists and is open
    const survey = await prisma.survey.findUnique({
      where: { id: responseData.surveyId }
    });

    if (!survey) {
      const error = new Error('Survey not found');
      (error as any).statusCode = 404;
      (error as any).code = 'SURVEY_NOT_FOUND';
      throw error;
    }

    if (survey.status !== 'OPEN') {
      const error = new Error('Survey is closed and no longer accepting responses');
      (error as any).statusCode = 400;
      (error as any).code = 'SURVEY_CLOSED';
      throw error;
    }

    const response = await prisma.surveyResponse.create({
      data: {
        survey_id: responseData.surveyId,
        user_id: responseData.userId,
        answers: responseData.answers,
        submitted_at: responseData.submittedAt
      }
    });

    // Update participants count
    await prisma.survey.update({
      where: { id: responseData.surveyId },
      data: {
        participants_count: {
          increment: 1
        }
      }
    });

    return {
      id: response.id,
      surveyId: response.survey_id,
      userId: response.user_id,
      answers: response.answers,
      submittedAt: response.submitted_at
    };
  }

  async getSurveyResponses(surveyId: string): Promise<any[]> {
    const responses = await prisma.surveyResponse.findMany({
      where: { survey_id: surveyId },
      include: {
        user: {
          select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true
          }
        }
      },
      orderBy: { submitted_at: 'desc' }
    });

    return responses.map(response => ({
      id: response.id,
      surveyId: response.survey_id,
      userId: response.user_id,
      answers: response.answers,
      submittedAt: response.submitted_at,
      user: response.user
    }));
  }
}